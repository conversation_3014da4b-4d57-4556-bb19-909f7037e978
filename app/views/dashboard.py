"""
仪表盘相关路由
"""
from flask import Blueprint, render_template, jsonify, session, redirect, url_for, request, current_app
from app.models.database import get_db
from app.utils.decorators import teacher_required
from app.services.file_service import FileService
from datetime import datetime
import uuid
import json

dashboard_bp = Blueprint('dashboard', __name__, url_prefix='/dashboard')

@dashboard_bp.route('/')
@teacher_required
def index():
    """课程班级页面"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 获取当前教师的所有课程安排
        cursor.execute("""
            SELECT cs.id, cs.course_id, cs.class_id, cs.classroom_id, cs.day_of_week,
                   cs.start_time, cs.end_time, cs.status, cs.start_datetime, cs.end_datetime,
                   c.name as course_name, c.code as course_code,
                   cl.name as class_name,
                   cr.name as classroom_name
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classes cl ON cs.class_id = cl.id
            JOIN classrooms cr ON cs.classroom_id = cr.id
            WHERE cs.teacher_id = ?
            ORDER BY cs.day_of_week, cs.start_time
        """, (session.get('teacher_id'),))

        courses_raw = cursor.fetchall()
        conn.close()

        # 转换星期数字为中文
        day_names = {
            1: '周一', 2: '周二', 3: '周三', 4: '周四',
            5: '周五', 6: '周六', 7: '周日'
        }

        courses = []
        for course in courses_raw:
            course_dict = dict(course)
            # 如果day_of_week是数字，进行转换；如果已经是字符串，保持原样
            if isinstance(course['day_of_week'], int):
                course_dict['day_of_week'] = day_names.get(course['day_of_week'], f'周{course["day_of_week"]}')
            else:
                # 如果已经是字符串，保持原样或进行简单清理
                day_str = str(course['day_of_week'])
                if day_str.startswith('周'):
                    course_dict['day_of_week'] = day_str
                else:
                    course_dict['day_of_week'] = day_str
            courses.append(course_dict)



        return render_template("dashboard/index.html",
                             current_page="index",
                             courses=courses)

    except Exception as e:
        return render_template("dashboard/index.html",
                             current_page="index",
                             courses=[],
                             error=str(e))

@dashboard_bp.route('/start_class/<course_schedule_id>', methods=['POST'])
@teacher_required
def start_class(course_schedule_id):
    """开始上课"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 验证课程安排是否属于当前教师
        cursor.execute("""
            SELECT id, status FROM course_schedules
            WHERE id = ? AND teacher_id = ?
        """, (course_schedule_id, session.get('teacher_id')))

        course = cursor.fetchone()
        if not course:
            conn.close()
            return jsonify({"status": "error", "message": "课程不存在或无权限"}), 403

        if course['status'] == 'in_progress':
            conn.close()
            return jsonify({"status": "error", "message": "课程已经在进行中"}), 400

        # 结束其他正在进行的课程
        cursor.execute("""
            UPDATE course_schedules
            SET status = 'completed', end_datetime = ?
            WHERE teacher_id = ? AND status = 'in_progress'
        """, (datetime.now().isoformat(), session.get('teacher_id')))

        # 开始当前课程
        cursor.execute("""
            UPDATE course_schedules
            SET status = 'in_progress', start_datetime = ?
            WHERE id = ?
        """, (datetime.now().isoformat(), course_schedule_id))

        # 设置当前选中的课堂
        session['current_class_id'] = course_schedule_id

        conn.commit()
        conn.close()

        return jsonify({"status": "success", "message": "课程已开始"})

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@dashboard_bp.route('/enter_class/<course_schedule_id>')
@teacher_required
def enter_class(course_schedule_id):
    """进入上课系统"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 验证课程安排是否属于当前教师
        cursor.execute("""
            SELECT id, status FROM course_schedules
            WHERE id = ? AND teacher_id = ?
        """, (course_schedule_id, session.get('teacher_id')))

        course = cursor.fetchone()
        if not course:
            conn.close()
            return redirect(url_for('index'))

        # 设置当前选中的课堂
        session['current_class_id'] = course_schedule_id
        conn.close()

        # 重定向到上课系统
        return redirect(url_for('teacher.index'))

    except Exception as e:
        return redirect(url_for('index'))


@dashboard_bp.route('/exercises')
@teacher_required
def exercises():
    """习题库页面"""
    return render_template("dashboard/exercises.html", current_page="exercises")


@dashboard_bp.route('/papers')
@teacher_required
def papers():
    """试卷库页面"""
    return render_template("dashboard/papers.html", current_page="papers")


@dashboard_bp.route('/materials')
@teacher_required
def materials():
    """课件库页面"""
    try:
        teacher_id = session.get('teacher_id')
        file_service = FileService()

        # 获取文件夹树结构
        success, message, folders = file_service.get_folder_tree(teacher_id)
        if not success:
            folders = []

        # 获取课程安排用于分配功能
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT cs.id, c.name as course_name, cl.name as class_name, cs.day_of_week, cs.start_time, cs.end_time
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classes cl ON cs.class_id = cl.id
            WHERE cs.teacher_id = ?
            ORDER BY cs.day_of_week, cs.start_time
        """, (teacher_id,))

        course_schedules = cursor.fetchall()
        conn.close()

        return render_template("dashboard/materials.html",
                             current_page="materials",
                             folders=folders,
                             course_schedules=course_schedules)
    except Exception as e:
        current_app.logger.error(f"Materials page error: {str(e)}")
        return render_template("dashboard/materials.html",
                             current_page="materials",
                             folders=[],
                             course_schedules=[])


# ==================== Exercises API Routes ====================

@dashboard_bp.route('/get_exercises', methods=['GET'])
@teacher_required
def get_exercises():
    """获取习题列表"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 获取筛选参数
        keyword = request.args.get('keyword', '').strip()
        types = request.args.get('types', '').strip()
        difficulties = request.args.get('difficulties', '').strip()

        # 构建基础查询
        base_query = """
            SELECT id, type, question, options, answer, difficulty, created_at, updated_at
            FROM exercises
            WHERE teacher_id = ?
        """
        params = [session.get('teacher_id')]

        # 添加搜索条件
        if keyword:
            base_query += " AND question LIKE ?"
            params.append(f'%{keyword}%')

        # 添加题型筛选
        if types:
            type_list = types.split(',')
            placeholders = ','.join(['?' for _ in type_list])
            base_query += f" AND type IN ({placeholders})"
            params.extend(type_list)

        # 添加难度筛选
        if difficulties:
            difficulty_list = difficulties.split(',')
            placeholders = ','.join(['?' for _ in difficulty_list])
            base_query += f" AND difficulty IN ({placeholders})"
            params.extend(difficulty_list)

        # 添加排序
        base_query += " ORDER BY created_at DESC"

        cursor.execute(base_query, params)
        exercises_data = cursor.fetchall()

        # 转换为字典列表
        exercises = []
        for row in exercises_data:
            exercise = dict(row)
            # 解析选项（如果是JSON字符串）
            if exercise['options']:
                try:
                    exercise['options'] = json.loads(exercise['options'])
                except:
                    pass
            exercises.append(exercise)

        conn.close()
        return jsonify({"status": "success", "exercises": exercises})

    except Exception as e:
        return jsonify({"status": "error", "message": f"获取习题失败: {str(e)}"}), 500


@dashboard_bp.route('/get_exercise/<exercise_id>', methods=['GET'])
@teacher_required
def get_exercise(exercise_id):
    """获取单个习题详情"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 查询习题详情，验证权限
        cursor.execute("""
            SELECT id, type, question, options, answer, difficulty, folder_path, created_at, updated_at
            FROM exercises
            WHERE id = ? AND teacher_id = ?
        """, (exercise_id, session.get('teacher_id')))

        exercise_row = cursor.fetchone()
        if not exercise_row:
            conn.close()
            return jsonify({"status": "error", "message": "习题不存在或无权限访问"}), 404

        exercise = dict(exercise_row)

        # 解析选项（如果是JSON字符串）
        if exercise['options']:
            try:
                exercise['options'] = json.loads(exercise['options'])
            except:
                pass

        conn.close()
        return jsonify({"status": "success", "exercise": exercise})

    except Exception as e:
        return jsonify({"status": "error", "message": f"获取习题详情失败: {str(e)}"}), 500


@dashboard_bp.route('/save_exercise', methods=['POST'])
@teacher_required
def save_exercise():
    """保存新习题"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        conn = get_db()
        cursor = conn.cursor()

        # 生成唯一ID
        exercise_id = str(uuid.uuid4())

        # 处理选项数据
        options_json = json.dumps(data.get('options', []), ensure_ascii=False) if data.get('options') else None

        # 插入习题
        cursor.execute("""
            INSERT INTO exercises (id, teacher_id, type, question, options, answer, difficulty, folder_path, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            exercise_id,
            session.get('teacher_id'),
            data.get('type'),
            data.get('question'),
            options_json,
            data.get('answer'),
            data.get('difficulty'),
            data.get('folder_path', '/'),
            datetime.now().isoformat(),
            datetime.now().isoformat()
        ))

        conn.commit()
        conn.close()

        return jsonify({"status": "success", "message": "习题保存成功", "exercise_id": exercise_id})

    except Exception as e:
        return jsonify({"status": "error", "message": f"保存习题失败: {str(e)}"}), 500


@dashboard_bp.route('/update_exercise/<exercise_id>', methods=['PUT'])
@teacher_required
def update_exercise(exercise_id):
    """更新习题"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        conn = get_db()
        cursor = conn.cursor()

        # 验证权限
        cursor.execute("SELECT id FROM exercises WHERE id = ? AND teacher_id = ?",
                      (exercise_id, session.get('teacher_id')))
        if not cursor.fetchone():
            conn.close()
            return jsonify({"status": "error", "message": "习题不存在或无权限访问"}), 404

        # 处理选项数据
        options_json = json.dumps(data.get('options', []), ensure_ascii=False) if data.get('options') else None

        # 更新习题
        cursor.execute("""
            UPDATE exercises
            SET type = ?, question = ?, options = ?, answer = ?, difficulty = ?, folder_path = ?, updated_at = ?
            WHERE id = ? AND teacher_id = ?
        """, (
            data.get('type'),
            data.get('question'),
            options_json,
            data.get('answer'),
            data.get('difficulty'),
            data.get('folder_path', '/'),
            datetime.now().isoformat(),
            exercise_id,
            session.get('teacher_id')
        ))

        conn.commit()
        conn.close()

        return jsonify({"status": "success", "message": "习题更新成功"})

    except Exception as e:
        return jsonify({"status": "error", "message": f"更新习题失败: {str(e)}"}), 500


@dashboard_bp.route('/delete_exercise/<exercise_id>', methods=['DELETE'])
@teacher_required
def delete_exercise(exercise_id):
    """删除习题"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 验证权限并删除
        cursor.execute("DELETE FROM exercises WHERE id = ? AND teacher_id = ?",
                      (exercise_id, session.get('teacher_id')))

        if cursor.rowcount == 0:
            conn.close()
            return jsonify({"status": "error", "message": "习题不存在或无权限访问"}), 404

        conn.commit()
        conn.close()

        return jsonify({"status": "success", "message": "习题删除成功"})

    except Exception as e:
        return jsonify({"status": "error", "message": f"删除习题失败: {str(e)}"}), 500


# ==================== Papers API Routes ====================

@dashboard_bp.route('/add_paper', methods=['GET', 'POST'])
@teacher_required
def add_paper():
    """新增试卷"""
    if request.method == 'POST':
        try:
            paper = request.get_json()
            if not paper:
                return jsonify({"status": "error", "message": "无效的请求数据"}), 400

            # 生成唯一ID（如果没有提供）
            if 'id' not in paper:
                paper['id'] = str(uuid.uuid4())

            conn = get_db()
            cursor = conn.cursor()

            # 试卷存储到资源库，不关联课程

            # 将整个试卷数据转换为JSON字符串存储到papers表
            cursor.execute(
                "INSERT INTO papers (id, title, description, teacher_id, created_at, data) VALUES (?, ?, ?, ?, ?, ?)",
                (
                    paper['id'],
                    paper.get('title', '未命名试卷'),
                    paper.get('description', ''),
                    session.get('teacher_id'),
                    datetime.now().isoformat(),
                    json.dumps(paper, ensure_ascii=False)
                )
            )

            conn.commit()
            conn.close()

            return jsonify({"status": "success"})
        except Exception as e:
            return jsonify({"status": "error", "message": str(e)}), 500

    # GET 请求：处理课程ID参数
    course_id = request.args.get('course_id')
    conn = get_db()
    cursor = conn.cursor()

    # 如果提供了course_id，获取该课程的信息并验证权限
    current_course = None
    if course_id:
        cursor.execute("""
            SELECT cs.id, c.name as course_name, c.code as course_code,
                    cl.name as classroom_name, cls.name as class_name,
                    cs.day_of_week, cs.start_time, cs.end_time
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classrooms cl ON cs.classroom_id = cl.id
            JOIN classes cls ON cs.class_id = cls.id
            WHERE cs.id = ? AND cs.teacher_id = ?
        """, (course_id, session.get('teacher_id')))
        current_course = cursor.fetchone()

    # 获取当前教师的所有课程安排（用于备选）
    cursor.execute("""
        SELECT cs.id, c.name as course_name, c.code as course_code,
                cl.name as classroom_name, cls.name as class_name,
                cs.day_of_week, cs.start_time, cs.end_time
        FROM course_schedules cs
        JOIN courses c ON cs.course_id = c.id
        JOIN classrooms cl ON cs.classroom_id = cl.id
        JOIN classes cls ON cs.class_id = cls.id
        WHERE cs.teacher_id = ?
        ORDER BY c.name, cs.day_of_week, cs.start_time
    """, (session.get('teacher_id'),))

    course_schedules = cursor.fetchall()
    conn.close()

    return render_template("dashboard/add_paper.html",
                            course_schedules=course_schedules,
                            current_course=current_course,
                            current_page="add_paper")
@dashboard_bp.route('/get_papers', methods=['GET'])
@teacher_required
def get_papers():
    """获取试卷列表"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 获取筛选参数
        keyword = request.args.get('keyword', '').strip()
        resource_only = request.args.get('resource_only', '').lower() == 'true'

        # 构建基础查询
        if resource_only:
            # 只查询资源库中的试卷
            base_query = """
                SELECT id, title, description, created_at, data
                FROM papers
                WHERE teacher_id = ?
            """
            params = [session.get('teacher_id')]
        else:
            # 查询所有试卷（包括作业中的试卷）
            base_query = """
                SELECT id, title, description, created_at, data, 'paper' as type
                FROM papers
                WHERE teacher_id = ?
                UNION ALL
                SELECT id, title, description, created_at, data, 'homework' as type
                FROM homework
                WHERE teacher_id = ?
            """
            params = [session.get('teacher_id'), session.get('teacher_id')]

        # 添加搜索条件
        if keyword:
            if resource_only:
                base_query += " AND title LIKE ?"
                params.append(f'%{keyword}%')
            else:
                # 对于UNION查询，需要重新构建
                base_query = """
                    SELECT id, title, description, created_at, data, 'paper' as type
                    FROM papers
                    WHERE teacher_id = ? AND title LIKE ?
                    UNION ALL
                    SELECT id, title, description, created_at, data, 'homework' as type
                    FROM homework
                    WHERE teacher_id = ? AND title LIKE ?
                """
                params = [session.get('teacher_id'), f'%{keyword}%', session.get('teacher_id'), f'%{keyword}%']

        # 添加排序
        base_query += " ORDER BY created_at DESC"

        cursor.execute(base_query, params)
        papers_data = cursor.fetchall()

        # 转换为字典列表
        papers = []
        for row in papers_data:
            paper = dict(row)
            # 解析试卷数据
            if paper.get('data'):
                try:
                    paper_data = json.loads(paper['data'])
                    paper.update(paper_data)
                except:
                    pass
            # 设置类型
            if 'type' not in paper:
                paper['type'] = '试卷'
            papers.append(paper)

        conn.close()
        return jsonify({"status": "success", "papers": papers})

    except Exception as e:
        return jsonify({"status": "error", "message": f"获取试卷失败: {str(e)}"}), 500


@dashboard_bp.route('/paper/<paper_id>', methods=['GET'])
@teacher_required
def get_paper(paper_id):
    """获取单个试卷"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 先尝试从papers表查询
        cursor.execute("SELECT data FROM papers WHERE id = ? AND teacher_id = ?", (paper_id, session.get('teacher_id')))
        paper_row = cursor.fetchone()

        # 如果papers表中没有，再从homework表查询
        if not paper_row:
            cursor.execute("SELECT data FROM homework WHERE id = ? AND teacher_id = ?", (paper_id, session.get('teacher_id')))
            paper_row = cursor.fetchone()

        conn.close()

        if not paper_row:
            return jsonify({"status": "error", "message": "试卷不存在"}), 404

        # 返回试卷数据
        paper = json.loads(paper_row[0])

        return jsonify(paper)
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500


@dashboard_bp.route('/paper/<paper_id>', methods=['DELETE'])
@teacher_required
def delete_paper(paper_id):
    """删除试卷"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 先尝试从papers表删除
        cursor.execute("DELETE FROM papers WHERE id = ? AND teacher_id = ?",
                      (paper_id, session.get('teacher_id')))

        deleted_count = cursor.rowcount

        # 如果papers表中没有，再尝试从homework表删除
        if deleted_count == 0:
            cursor.execute("DELETE FROM homework WHERE id = ? AND teacher_id = ?",
                          (paper_id, session.get('teacher_id')))
            deleted_count = cursor.rowcount

        if deleted_count == 0:
            conn.close()
            return jsonify({"status": "error", "message": "试卷不存在或无权限访问"}), 404

        conn.commit()
        conn.close()

        return jsonify({"status": "success", "message": "试卷删除成功"})

    except Exception as e:
        return jsonify({"status": "error", "message": f"删除试卷失败: {str(e)}"}), 500


# ==================== Materials API Routes ====================

@dashboard_bp.route('/get_folders', methods=['GET'])
@teacher_required
def get_folders():
    """获取文件夹树结构"""
    try:
        teacher_id = session.get('teacher_id')
        file_service = FileService()

        success, message, folders = file_service.get_folder_tree(teacher_id)
        if success:
            return jsonify({"status": "success", "folders": folders})
        else:
            return jsonify({"status": "error", "message": message}), 500

    except Exception as e:
        return jsonify({"status": "error", "message": f"获取文件夹失败: {str(e)}"}), 500


@dashboard_bp.route('/get_files', methods=['GET'])
@teacher_required
def get_files():
    """获取文件列表"""
    try:
        teacher_id = session.get('teacher_id')
        folder_id = request.args.get('folder_id', type=int)
        search_term = request.args.get('search', '').strip()

        file_service = FileService()
        success, message, files = file_service.get_files_in_folder(teacher_id, folder_id, search_term)

        if success:
            return jsonify({"status": "success", "files": files})
        else:
            return jsonify({"status": "error", "message": message}), 500

    except Exception as e:
        return jsonify({"status": "error", "message": f"获取文件列表失败: {str(e)}"}), 500


@dashboard_bp.route('/upload_files', methods=['POST'])
@teacher_required
def upload_files():
    """上传文件"""
    try:
        if 'files' not in request.files:
            return jsonify({"status": "error", "message": "没有选择文件"}), 400

        files = request.files.getlist('files')
        if not files or all(file.filename == '' for file in files):
            return jsonify({"status": "error", "message": "没有选择文件"}), 400

        teacher_id = session.get('teacher_id')
        folder_id = request.form.get('folder_id', type=int)

        file_service = FileService()
        uploaded_files = []
        failed_files = []

        for file in files:
            if file.filename == '':
                continue

            success, message, file_id = file_service.upload_file(teacher_id, file, folder_id)
            if success:
                uploaded_files.append(file.filename)
            else:
                failed_files.append(f"{file.filename}: {message}")

        if uploaded_files:
            response_message = f"成功上传 {len(uploaded_files)} 个文件"
            if failed_files:
                response_message += f"，{len(failed_files)} 个文件上传失败"

            return jsonify({
                "status": "success",
                "message": response_message,
                "uploaded_files": uploaded_files,
                "failed_files": failed_files
            })
        else:
            return jsonify({
                "status": "error",
                "message": "所有文件上传失败",
                "failed_files": failed_files
            }), 400

    except Exception as e:
        return jsonify({"status": "error", "message": f"上传失败: {str(e)}"}), 500


@dashboard_bp.route('/delete_file/<int:file_id>', methods=['DELETE'])
@teacher_required
def delete_file(file_id):
    """删除文件"""
    try:
        teacher_id = session.get('teacher_id')
        file_service = FileService()

        success, message = file_service.delete_file(teacher_id, file_id)

        if success:
            return jsonify({"status": "success", "message": message})
        else:
            return jsonify({"status": "error", "message": message}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": f"删除文件失败: {str(e)}"}), 500


@dashboard_bp.route('/delete_files', methods=['DELETE'])
@teacher_required
def delete_files():
    """批量删除文件"""
    try:
        data = request.get_json()
        if not data or 'file_ids' not in data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        file_ids = data['file_ids']
        if not isinstance(file_ids, list) or not file_ids:
            return jsonify({"status": "error", "message": "文件ID列表不能为空"}), 400

        teacher_id = session.get('teacher_id')
        file_service = FileService()

        deleted_count = 0
        failed_count = 0

        for file_id in file_ids:
            success, message = file_service.delete_file(teacher_id, file_id)
            if success:
                deleted_count += 1
            else:
                failed_count += 1

        if deleted_count > 0:
            message = f"成功删除 {deleted_count} 个文件"
            if failed_count > 0:
                message += f"，{failed_count} 个文件删除失败"
            return jsonify({"status": "success", "message": message})
        else:
            return jsonify({"status": "error", "message": "所有文件删除失败"}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": f"批量删除失败: {str(e)}"}), 500


@dashboard_bp.route('/create_folder', methods=['POST'])
@teacher_required
def create_folder():
    """创建文件夹"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        folder_name = data.get('folder_name', '').strip()
        parent_id = data.get('parent_id', type=int)

        if not folder_name:
            return jsonify({"status": "error", "message": "文件夹名称不能为空"}), 400

        teacher_id = session.get('teacher_id')
        file_service = FileService()

        success, message, folder_id = file_service.create_folder(teacher_id, folder_name, parent_id)

        if success:
            return jsonify({"status": "success", "message": message, "folder_id": folder_id})
        else:
            return jsonify({"status": "error", "message": message}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": f"创建文件夹失败: {str(e)}"}), 500


@dashboard_bp.route('/delete_folder/<int:folder_id>', methods=['DELETE'])
@teacher_required
def delete_folder(folder_id):
    """删除文件夹"""
    try:
        teacher_id = session.get('teacher_id')
        file_service = FileService()

        success, message = file_service.delete_folder(teacher_id, folder_id)

        if success:
            return jsonify({"status": "success", "message": message})
        else:
            return jsonify({"status": "error", "message": message}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": f"删除文件夹失败: {str(e)}"}), 500


@dashboard_bp.route('/move_file', methods=['POST'])
@teacher_required
def move_file():
    """移动文件到指定文件夹"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        file_id = data.get('file_id', type=int)
        target_folder_id = data.get('target_folder_id', type=int)

        if not file_id:
            return jsonify({"status": "error", "message": "文件ID不能为空"}), 400

        teacher_id = session.get('teacher_id')
        file_service = FileService()

        success, message = file_service.move_file(teacher_id, file_id, target_folder_id)

        if success:
            return jsonify({"status": "success", "message": message})
        else:
            return jsonify({"status": "error", "message": message}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": f"移动文件失败: {str(e)}"}), 500


@dashboard_bp.route('/move_files', methods=['POST'])
@teacher_required
def move_files():
    """批量移动文件"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        file_ids = data.get('file_ids', [])
        target_folder_id = data.get('target_folder_id', type=int)

        if not file_ids:
            return jsonify({"status": "error", "message": "文件ID列表不能为空"}), 400

        teacher_id = session.get('teacher_id')
        file_service = FileService()

        moved_count = 0
        failed_count = 0

        for file_id in file_ids:
            success, message = file_service.move_file(teacher_id, file_id, target_folder_id)
            if success:
                moved_count += 1
            else:
                failed_count += 1

        if moved_count > 0:
            message = f"成功移动 {moved_count} 个文件"
            if failed_count > 0:
                message += f"，{failed_count} 个文件移动失败"
            return jsonify({"status": "success", "message": message})
        else:
            return jsonify({"status": "error", "message": "所有文件移动失败"}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": f"批量移动失败: {str(e)}"}), 500


@dashboard_bp.route('/rename_file', methods=['POST'])
@teacher_required
def rename_file():
    """重命名文件"""
    try:
        from app.services.file_service import FileService

        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        file_id = data.get('file_id', type=int)
        new_name = data.get('new_name', '').strip()

        if not file_id:
            return jsonify({"status": "error", "message": "文件ID不能为空"}), 400

        if not new_name:
            return jsonify({"status": "error", "message": "新文件名不能为空"}), 400

        teacher_id = session.get('teacher_id')
        file_service = FileService()

        success, message = file_service.rename_file(teacher_id, file_id, new_name)

        if success:
            return jsonify({"status": "success", "message": message})
        else:
            return jsonify({"status": "error", "message": message}), 400

    except Exception as e:
        return jsonify({"status": "error", "message": f"重命名文件失败: {str(e)}"}), 500


@dashboard_bp.route('/download_file/<int:file_id>')
@teacher_required
def download_file(file_id):
    """下载文件"""
    try:
        from app.services.file_service import FileService
        from flask import send_file, current_app
        import os

        teacher_id = session.get('teacher_id')
        file_service = FileService()

        success, message, file_info = file_service.get_file_info(teacher_id, file_id)

        if not success:
            return jsonify({"status": "error", "message": message}), 404

        # 构建文件路径
        file_path = os.path.join(current_app.config.get('UPLOAD_FOLDER', 'uploads'), file_info['file_path'])

        if not os.path.exists(file_path):
            return jsonify({"status": "error", "message": "文件不存在"}), 404

        return send_file(file_path,
                        as_attachment=True,
                        download_name=file_info['original_filename'],
                        mimetype=file_info['mime_type'])

    except Exception as e:
        return jsonify({"status": "error", "message": f"下载文件失败: {str(e)}"}), 500


@dashboard_bp.route('/assign_file_to_class', methods=['POST'])
@teacher_required
def assign_file_to_class():
    """将文件分配给课堂"""
    try:
        from app.services.file_service import FileService

        data = request.get_json()
        if not data:
            return jsonify({"status": "error", "message": "无效的请求数据"}), 400

        file_id = data.get('file_id', type=int)
        schedule_id = data.get('schedule_id')

        if not file_id or not schedule_id:
            return jsonify({"status": "error", "message": "文件ID和课程安排ID不能为空"}), 400

        teacher_id = session.get('teacher_id')
        file_service = FileService()

        # 获取文件信息
        success, message, file_info = file_service.get_file_info(teacher_id, file_id)
        if not success:
            return jsonify({"status": "error", "message": message}), 404

        # 验证课程安排权限
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT id FROM course_schedules
            WHERE id = ? AND teacher_id = ?
        """, (schedule_id, teacher_id))

        if not cursor.fetchone():
            conn.close()
            return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

        # 创建课件记录关联到课程
        material_id = str(uuid.uuid4())
        cursor.execute("""
            INSERT INTO teacher_materials (id, teacher_id, title, file_path, file_type, file_size, folder_path, upload_time, description, course_schedule_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            material_id,
            teacher_id,
            file_info['original_filename'],
            file_info['file_path'],
            file_info['mime_type'],
            file_info['file_size'],
            '/',
            datetime.now().isoformat(),
            '',
            schedule_id
        ))

        conn.commit()
        conn.close()

        return jsonify({"status": "success", "message": "文件分配成功"})

    except Exception as e:
        return jsonify({"status": "error", "message": f"分配文件失败: {str(e)}"}), 500