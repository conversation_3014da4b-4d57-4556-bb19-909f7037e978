{% extends "/teacher/base.html" %}

{% block title %}新建作业{% endblock %}

{% block styles %}
<link rel="stylesheet" href="/static/css/add_paper.css">
<style>
.paper-type-info {
    margin-bottom: 20px;
    padding: 15px;
    border-radius: 6px;
    background: #f8f9fa;
    border-left: 4px solid #1E9FFF;
}

.course-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.course-name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.class-name {
    font-size: 14px;
    color: #666;
}

.layui-form-item.deadline-item {
    margin-top: 20px;
}

.deadline-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

/* 导入习题弹窗样式 */
.import-exercise-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.search-filter-container {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.search-box {
    display: flex;
    flex: 1;
    min-width: 250px;
}

.search-input-wrapper {
    flex: 1;
}

.search-btn {
    margin-left: 10px;
    height: 38px;
    line-height: 38px;
    padding: 0 18px;
    background-color: #1E9FFF;
    color: #fff;
    white-space: nowrap;
    text-align: center;
    border: none;
    border-radius: 2px;
    cursor: pointer;
}

.filter-box {
    position: relative;
}

.filter-dropdown {
    position: relative;
}

.filter-btn {
    display: flex;
    align-items: center;
    height: 38px;
    line-height: 38px;
    padding: 0 18px;
    background-color: #fff;
    color: #666;
    white-space: nowrap;
    text-align: center;
    border: 1px solid #e6e6e6;
    border-radius: 2px;
    cursor: pointer;
}

.filter-btn .arrow-icon {
    margin-left: 5px;
    font-size: 12px;
}

.filter-panel {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 100;
    width: 300px;
    background-color: #fff;
    border: 1px solid #e6e6e6;
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    padding: 15px;
    margin-top: 5px;
}

.filter-section {
    margin-bottom: 15px;
}

.filter-label {
    font-weight: bold;
    margin-bottom: 8px;
}

.filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.filter-option {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.filter-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
}

.clear-filter-btn {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
}

.confirm-filter-btn {
    background-color: #1E9FFF;
    color: #fff;
    border: none;
    padding: 6px 15px;
    border-radius: 2px;
    cursor: pointer;
}

.import-exercise-list {
    flex: 1;
    overflow-y: auto;
    border: 1px solid #e6e6e6;
}

.type-single {
    background-color: #5FB878 !important;
}

.type-multiple {
    background-color: #1E9FFF !important;
}

.type-judge {
    background-color: #FFB800 !important;
}

.type-fill {
    background-color: #FF5722 !important;
}

.type-subjective {
    background-color: #01AAED !important;
}

.difficulty-easy {
    background-color: #5FB878 !important;
}

.difficulty-medium {
    background-color: #FFB800 !important;
}

.difficulty-hard {
    background-color: #FF5722 !important;
}

/* 对齐按钮 */
.setting-actions .layui-btn {
    width: 100%; /* 确保按钮占据全部宽度 */
    box-sizing: border-box; /* 确保 padding 和 border 不会增加额外宽度 */
    margin-left: 0; /* 重置左外边距 */
    margin-right: 0; /* 重置右外边距 */
}
</style>
{% endblock %}

{% block content %}
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <!-- 返回按钮 -->
        <div class="layui-col-md12">
            <button class="layui-btn layui-btn-primary back-btn" onclick="window.location.href='/teacher/homework'">
                <i class="layui-icon layui-icon-left"></i> 返回作业列表
            </button>
        </div>
        
        <!-- 左侧菜单栏 -->
        <div class="layui-col-md3 sidebar-fixed">
            <div class="layui-card">
                <div class="layui-card-header">题目类型</div>
                <div class="layui-card-body">
                    <div class="question-type-buttons">
                        <div class="button-row">
                            <button type="button" class="layui-btn layui-btn-primary question-type-btn" onclick="addQuestion('single')">
                                <i class="layui-icon layui-icon-radio"></i>
                                单选题
                            </button>
                            <button type="button" class="layui-btn layui-btn-primary question-type-btn" onclick="addQuestion('multiple')">
                                <i class="layui-icon layui-icon-checkbox-circle"></i>
                                多选题
                            </button>
                        </div>
                        <div class="button-row">
                            <button type="button" class="layui-btn layui-btn-primary question-type-btn" onclick="addQuestion('judge')">
                                <i class="layui-icon layui-icon-about"></i>
                                判断题
                            </button>
                            <button type="button" class="layui-btn layui-btn-primary question-type-btn" onclick="addQuestion('fill')">
                                <i class="layui-icon layui-icon-edit"></i>
                                填空题
                            </button>
                        </div>
                        <div class="button-row single-button">
                            <button type="button" class="layui-btn layui-btn-primary question-type-btn" onclick="addQuestion('subjective')">
                                <i class="layui-icon layui-icon-form"></i>
                                主观题
                            </button>
                        </div>
                        <div class="button-row single-button import-row">
                            <button type="button" class="layui-btn layui-btn-normal question-type-btn import-btn" onclick="openImportExerciseModal()">
                                <i class="layui-icon layui-icon-template-1"></i>
                                从题库导入
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 中间内容区域 -->
        <div class="layui-col-md6 content-fixed">
            <div class="layui-card content-card">
                <div class="layui-card-header paper-info-header">
                    <!-- 显示课程信息 -->
                    <div class="paper-type-info">
                        <div class="course-info">
                            <span class="course-name">{{ current_class.course_name }}</span>
                        </div>
                    </div>
                    <div class="paper-basic-info">
                        <div class="layui-form-item">
                            <input type="text" id="homework-title" lay-verify="required" placeholder="作业标题" autocomplete="off" class="layui-input">
                        </div>
                        <div class="layui-form-item">
                            <textarea id="homework-description" placeholder="作业说明（可选）" class="layui-textarea" rows="2"></textarea>
                        </div>
                    </div>
                </div>
                <div class="layui-card-body content-scrollable">
                    <div id="questions">
                        <p id="no-questions-message" style="color: gray; text-align: center;">点击左侧按钮添加题目</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧设置区域 -->
        <div class="layui-col-md3 sidebar-fixed">
            <div class="layui-card">
                <div class="layui-card-header">
                    <i class="layui-icon layui-icon-set"></i>
                    作业设置
                </div>
                <div class="layui-card-body paper-settings">
                    <div class="layui-form-item deadline-item">
                        <label class="deadline-label">截止时间</label>
                        <input type="date" id="deadline" class="layui-input" required>
                    </div>

                    <div class="setting-item total-score-item">
                        <div class="setting-label">
                            <i class="layui-icon layui-icon-star"></i>
                            总分
                        </div>
                        <div class="total-score-display">
                            <span id="total-score">0</span> 分
                        </div>
                    </div>

                    <div class="setting-actions">
                        <button type="button" class="layui-btn layui-btn-normal layui-btn-fluid" onclick="saveAsDraft()">
                            <i class="layui-icon layui-icon-template"></i>
                            保存为草稿
                        </button>
                        <button type="button" class="layui-btn layui-btn-warm layui-btn-fluid" onclick="publishHomework()" style="margin-top: 10px;">
                            <i class="layui-icon layui-icon-release"></i>
                            立即发布
                        </button>
                        <button type="button" class="layui-btn layui-btn-fluid" onclick="cancelHomework()" style="margin-top: 10px;">
                            <i class="layui-icon layui-icon-close"></i>
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 导入习题弹窗 -->
<div id="importExerciseModal" style="display: none; padding: 20px;">
    <div class="import-exercise-container">
        <!-- 搜索和筛选区域 -->
        <div class="search-filter-container">
            <!-- 搜索框 -->
            <div class="search-box">
                <div class="search-input-wrapper">
                    <div class="layui-input-group">
                        <div class="layui-input-prefix">
                            <i class="layui-icon layui-icon-search"></i>
                        </div>
                        <input type="text" id="importSearchInput" placeholder="搜索习题内容" class="layui-input">
                        <div class="layui-input-suffix">
                            <button type="button" id="importClearSearchBtn" class="clear-btn" style="display: none;">
                                <i class="layui-icon layui-icon-close"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <button type="button" id="importSearchBtn" class="search-btn">搜索</button>
            </div>

            <!-- 筛选按钮 -->
            <div class="filter-box">
                <div class="filter-dropdown">
                    <button type="button" class="filter-btn" id="importFilterBtn">
                        <i class="layui-icon layui-icon-screen"></i>
                        <span>筛选</span>
                        <span id="importFilterText">全部</span>
                        <i class="layui-icon layui-icon-down arrow-icon"></i>
                    </button>

                    <!-- 筛选下拉面板 -->
                    <div class="filter-panel" id="importFilterPanel" style="display: none;">
                        <!-- 题型筛选 -->
                        <div class="filter-section">
                            <div class="filter-label">题型：</div>
                            <div class="filter-options">
                                <label class="filter-option">
                                    <input type="checkbox" name="importTypeFilter" value="single">
                                    <span>单选题</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="importTypeFilter" value="multiple">
                                    <span>多选题</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="importTypeFilter" value="judge">
                                    <span>判断题</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="importTypeFilter" value="fill">
                                    <span>填空题</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="importTypeFilter" value="subjective">
                                    <span>主观题</span>
                                </label>
                            </div>
                        </div>

                        <!-- 难度筛选 -->
                        <div class="filter-section">
                            <div class="filter-label">难度：</div>
                            <div class="filter-options">
                                <label class="filter-option">
                                    <input type="checkbox" name="importDifficultyFilter" value="easy">
                                    <span>简单</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="importDifficultyFilter" value="medium">
                                    <span>中等</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="importDifficultyFilter" value="hard">
                                    <span>困难</span>
                                </label>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="filter-actions">
                            <button type="button" class="clear-filter-btn" id="importClearFilterBtn">清空条件</button>
                            <button type="button" class="confirm-filter-btn" id="importConfirmFilterBtn">确定</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 题目统计 -->
            <div class="question-count">
                <span id="importQuestionCountText">共 0 道题</span>
            </div>
        </div>

        <!-- 习题列表 -->
        <div class="import-exercise-list" id="importExerciseList">
            <table class="layui-table" lay-skin="line">
                <thead>
                    <tr>
                        <th width="50">
                            <input type="checkbox" id="selectAllExercises" lay-skin="primary">
                        </th>
                        <th width="80">序号</th>
                        <th width="100">题型</th>
                        <th>题目内容</th>
                        <th width="80">难度</th>
                        <th width="120">创建时间</th>
                    </tr>
                </thead>
                <tbody id="importExerciseTableBody">
                    <tr>
                        <td colspan="6" style="text-align: center; color: #999; padding: 40px;">
                            正在加载习题数据...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <div id="importPagination" style="text-align: center; margin-top: 20px;"></div>

        <!-- 操作按钮 -->
        <div class="import-actions" style="text-align: center; margin-top: 20px; border-top: 1px solid #e6e6e6; padding-top: 20px;">
            <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            <button type="button" class="layui-btn layui-btn-normal" id="confirmImportBtn">
                <i class="layui-icon layui-icon-ok"></i>
                导入选中习题
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let questionCount = 0;

function addQuestion(type) {
    const questionsDiv = document.getElementById("questions");
    const noQuestionsMessage = document.getElementById("no-questions-message");

    // 如果存在提示文字，则移除
    if (noQuestionsMessage) {
        noQuestionsMessage.remove();
    }

    const questionDiv = document.createElement("div");
    questionDiv.className = "layui-form-item question";

    // 动态计算当前题目数量作为新题目的序号
    const currentQuestionCount = questionsDiv.querySelectorAll('.question').length;
    questionDiv.id = `question-${currentQuestionCount}`;

    let html = `
        <input type="hidden" id="type-${currentQuestionCount}" value="${type}">
        <div class="question-score">
            <div class="question-score-left">
                <label>分值：</label>
                <input type="number" id="score-${currentQuestionCount}" lay-verify="required|number" min="1" value="5" autocomplete="off" class="layui-input">
            </div>
            <button type="button" class="delete-btn" onclick="removeQuestion(${currentQuestionCount})">删除题目</button>
        </div>
        <div class="layui-form-item question-header">
            <span class="question-number">${currentQuestionCount + 1}.</span>
            <input type="text" id="desc-${currentQuestionCount}" lay-verify="required" placeholder="请输入题目" autocomplete="off" class="layui-input">
        </div>
    `;

    if (type === "single" || type === "multiple") {
        const inputType = type === "single" ? "radio" : "checkbox";
        html += `<div class="options-container" id="options-container-${currentQuestionCount}">`;

        // 默认添加4个选项
        for (let i = 0; i < 4; i++) {
            const optionLetter = String.fromCharCode(65 + i); // A, B, C, D...
            const isFirst = i === 0; // 默认第一个选项为正确答案

            html += `
                <div class="option-item ${isFirst ? 'correct' : ''}" data-index="${i}">
                    <div class="option-content">
                        <input type="${inputType}" name="option-${currentQuestionCount}" ${isFirst ? 'checked' : ''} class="option-checkbox" title="正确答案">
                        <span class="option-letter">${optionLetter}.</span>
                        <input type="text" class="layui-input option-text" placeholder="请输入选项内容">
                        ${isFirst ? '<span class="correct-answer">正确答案</span>' : ''}
                    </div>
                    <button type="button" class="option-delete-btn" onclick="deleteOption(${currentQuestionCount}, ${i})" title="删除选项"></button>
                </div>
            `;
        }

        html += `
            <div class="add-option">
                <button type="button" class="add-option-btn" onclick="addOption(${currentQuestionCount})">
                    <i class="layui-icon layui-icon-add-1"></i>
                    添加选项
                </button>
            </div>
        </div>
        <input type="hidden" id="answer-${currentQuestionCount}" value="A">
        `;
    }

    if (type === "judge") {
        html += `
            <div class="options-container">
                <div class="option-item judge-item correct" data-value="正确">
                    <div class="option-content">
                        <input type="radio" name="option-${currentQuestionCount}" checked class="option-checkbox" title="正确答案">
                        <span class="option-letter">A.</span>
                        <span class="fixed-option">正确</span>
                        <span class="correct-answer">正确答案</span>
                    </div>
                </div>
                <div class="option-item judge-item" data-value="错误">
                    <div class="option-content">
                        <input type="radio" name="option-${currentQuestionCount}" class="option-checkbox" title="错误">
                        <span class="option-letter">B.</span>
                        <span class="fixed-option">错误</span>
                    </div>
                </div>
            </div>
            <input type="hidden" id="answer-${currentQuestionCount}" value="正确">
        `;
    }

    if (type === "fill") {
        html += `
            <div class="layui-form-item fill-answer">
                <label class="layui-form-label">填空题答案：</label>
                <div class="layui-input-block">
                    <input type="text" id="answer-${currentQuestionCount}" lay-verify="required" autocomplete="off" class="layui-input">
                </div>
            </div>
        `;
    }

    if (type === "subjective") {
        html += `
            <div class="layui-form-item subjective-answer">
                <label class="layui-form-label">主观题答案：</label>
                <div class="layui-input-block">
                    <textarea id="answer-${currentQuestionCount}" lay-verify="required" class="layui-textarea" rows="4"></textarea>
                </div>
            </div>
        `;
    }

    questionDiv.innerHTML = html;
    questionsDiv.appendChild(questionDiv);

    // 每次添加题目后重新计算总分
    calculateTotalScore();

    // 为分值输入框绑定 input 事件，实时更新总分
    const scoreInput = document.getElementById(`score-${currentQuestionCount}`);
    scoreInput.addEventListener('input', calculateTotalScore);

    // 为选项添加事件监听
    if (type === "single" || type === "multiple") {
        const optionItems = questionDiv.querySelectorAll('.option-item');
        optionItems.forEach((item, index) => {
            const checkbox = item.querySelector('.option-checkbox');

            // 绑定整个选项项的点击事件
            item.addEventListener('click', function(e) {
                // 如果点击的是删除按钮或文本输入框，不处理
                if (e.target.classList.contains('option-delete-btn') ||
                    e.target.closest('.option-delete-btn') ||
                    e.target.classList.contains('option-text')) {
                    return;
                }

                // 阻止事件冒泡
                e.preventDefault();
                e.stopPropagation();

                if (type === "single") {
                    // 单选：移除所有正确答案标识
                    optionItems.forEach(optionItem => {
                        optionItem.querySelector('.option-checkbox').checked = false;
                        optionItem.classList.remove('correct');
                        const correctSpan = optionItem.querySelector('.correct-answer');
                        if (correctSpan) correctSpan.remove();
                    });

                    // 设置当前为正确答案
                    checkbox.checked = true;
                    item.classList.add('correct');
                    if (!item.querySelector('.correct-answer')) {
                        const correctSpan = document.createElement('span');
                        correctSpan.className = 'correct-answer';
                        correctSpan.textContent = '正确答案';
                        item.querySelector('.option-content').appendChild(correctSpan);
                    }

                    // 更新答案值
                    document.getElementById(`answer-${currentQuestionCount}`).value = String.fromCharCode(65 + index);
                } else if (type === "multiple") {
                    // 多选：切换选中状态
                    const isChecked = checkbox.checked;
                    checkbox.checked = !isChecked;

                    if (!isChecked) {
                        item.classList.add('correct');
                        if (!item.querySelector('.correct-answer')) {
                            const correctSpan = document.createElement('span');
                            correctSpan.className = 'correct-answer';
                            correctSpan.textContent = '正确答案';
                            item.querySelector('.option-content').appendChild(correctSpan);
                        }
                    } else {
                        item.classList.remove('correct');
                        const correctSpan = item.querySelector('.correct-answer');
                        if (correctSpan) correctSpan.remove();
                    }

                    // 更新多选答案，收集所有选中的选项
                    const selectedOptions = [];
                    optionItems.forEach((optionItem, idx) => {
                        if (optionItem.querySelector('.option-checkbox').checked) {
                            selectedOptions.push(String.fromCharCode(65 + idx));
                        }
                    });
                    document.getElementById(`answer-${currentQuestionCount}`).value = selectedOptions.join(',');
                }
            });

            // 单独绑定复选框的点击事件
            checkbox.addEventListener('click', function(e) {
                e.stopPropagation();
                // 触发父级的点击事件
                item.click();
            });

            // 阻止文本框的点击事件冒泡
            const optionText = item.querySelector('.option-text');
            if (optionText) {
                optionText.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }
        });
    } else if (type === "judge") {
        // 为判断题添加事件监听
        const judgeItems = questionDiv.querySelectorAll('.judge-item');
        judgeItems.forEach((item) => {
            const radio = item.querySelector('.option-checkbox');
            const answer = item.getAttribute('data-value');

            item.addEventListener('click', function(e) {
                if (e.target.closest('.judge-item')) {
                    // 移除所有正确答案标识
                    judgeItems.forEach(judgeItem => {
                        judgeItem.querySelector('.option-checkbox').checked = false;
                        const correctSpan = judgeItem.querySelector('.correct-answer');
                        if (correctSpan) correctSpan.remove();
                    });

                    // 设置当前为正确答案
                    radio.checked = true;
                    item.classList.add('correct');
                    if (!item.querySelector('.correct-answer')) {
                        const correctSpan = document.createElement('span');
                        correctSpan.className = 'correct-answer';
                        correctSpan.textContent = '正确答案';
                        item.querySelector('.option-content').appendChild(correctSpan);
                    }

                    // 更新答案值
                    document.getElementById(`answer-${currentQuestionCount}`).value = answer;
                }
            });
        });
    }

    questionCount++;
}

function deleteOption(questionId, optionIndex) {
    const container = document.getElementById(`options-container-${questionId}`);
    const options = container.querySelectorAll('.option-item');

    if (options.length <= 2) {
        layer.msg('至少需要保留2个选项', {icon: 2});
        return;
    }

    const optionToDelete = options[optionIndex];
    const wasCorrect = optionToDelete.classList.contains('correct');

    // 删除选项
    optionToDelete.remove();

    // 如果删除的是正确答案，则将第一个选项设为正确答案
    if (wasCorrect) {
        const firstOption = container.querySelector('.option-item');
        firstOption.classList.add('correct');
        firstOption.querySelector('.option-checkbox').checked = true;
        if (!firstOption.querySelector('.correct-answer')) {
            const correctSpan = document.createElement('span');
            correctSpan.className = 'correct-answer';
            correctSpan.textContent = '正确答案';
            firstOption.querySelector('.option-content').appendChild(correctSpan);
        }
        document.getElementById(`answer-${questionId}`).value = 'A';
    }

    // 重新编号选项
    const remainingOptions = container.querySelectorAll('.option-item');
    remainingOptions.forEach((option, index) => {
        const letter = String.fromCharCode(65 + index);
        option.querySelector('.option-letter').textContent = `${letter}.`;
    });
}

function addOption(questionId) {
    const container = document.getElementById(`options-container-${questionId}`);
    const options = container.querySelectorAll('.option-item');
    const newIndex = options.length;
    const optionLetter = String.fromCharCode(65 + newIndex);

    const optionDiv = document.createElement('div');
    optionDiv.className = 'option-item';
    optionDiv.setAttribute('data-index', newIndex);

    const type = document.getElementById(`type-${questionId}`).value;
    const inputType = type === 'single' ? 'radio' : 'checkbox';

    optionDiv.innerHTML = `
        <div class="option-content">
            <input type="${inputType}" name="option-${questionId}" class="option-checkbox" title="选项">
            <span class="option-letter">${optionLetter}.</span>
            <input type="text" class="layui-input option-text" placeholder="请输入选项内容">
        </div>
        <button type="button" class="option-delete-btn" onclick="deleteOption(${questionId}, ${newIndex})" title="删除选项"></button>
    `;

    // 将新选项插入到"添加选项"按钮之前
    const addOptionBtn = container.querySelector('.add-option');
    container.insertBefore(optionDiv, addOptionBtn);

    // 为新选项添加事件监听
    const checkbox = optionDiv.querySelector('.option-checkbox');
    const optionText = optionDiv.querySelector('.option-text');

    optionDiv.addEventListener('click', function(e) {
        if (e.target.classList.contains('option-delete-btn') ||
            e.target.closest('.option-delete-btn') ||
            e.target.classList.contains('option-text')) {
            return;
        }

        e.preventDefault();
        e.stopPropagation();

        if (type === 'single') {
            // 单选逻辑
            const allOptions = container.querySelectorAll('.option-item');
            allOptions.forEach(optionItem => {
                optionItem.querySelector('.option-checkbox').checked = false;
                optionItem.classList.remove('correct');
                const correctSpan = optionItem.querySelector('.correct-answer');
                if (correctSpan) correctSpan.remove();
            });

            checkbox.checked = true;
            optionDiv.classList.add('correct');
            const correctSpan = document.createElement('span');
            correctSpan.className = 'correct-answer';
            correctSpan.textContent = '正确答案';
            optionDiv.querySelector('.option-content').appendChild(correctSpan);

            document.getElementById(`answer-${questionId}`).value = optionLetter;
        } else {
            // 多选逻辑
            const isChecked = checkbox.checked;
            checkbox.checked = !isChecked;

            if (!isChecked) {
                optionDiv.classList.add('correct');
                const correctSpan = document.createElement('span');
                correctSpan.className = 'correct-answer';
                correctSpan.textContent = '正确答案';
                optionDiv.querySelector('.option-content').appendChild(correctSpan);
            } else {
                optionDiv.classList.remove('correct');
                const correctSpan = optionDiv.querySelector('.correct-answer');
                if (correctSpan) correctSpan.remove();
            }

            const selectedOptions = [];
            container.querySelectorAll('.option-item').forEach((optionItem, idx) => {
                if (optionItem.querySelector('.option-checkbox').checked) {
                    selectedOptions.push(String.fromCharCode(65 + idx));
                }
            });
            document.getElementById(`answer-${questionId}`).value = selectedOptions.join(',');
        }
    });

    checkbox.addEventListener('click', function(e) {
        e.stopPropagation();
        optionDiv.click();
    });

    optionText.addEventListener('click', function(e) {
        e.stopPropagation();
    });
}

function removeQuestion(id) {
    const questionDiv = document.getElementById(`question-${id}`);
    if (questionDiv) {
        questionDiv.remove();
        updateQuestionNumbers();
        calculateTotalScore();
    }
}

function updateQuestionNumbers() {
    const questions = document.querySelectorAll('.question');
    questions.forEach((question, index) => {
        const numberSpan = question.querySelector('.question-number');
        if (numberSpan) {
            numberSpan.textContent = `${index + 1}.`;
        }
    });
}

function calculateTotalScore() {
    let total = 0;
    const scoreInputs = document.querySelectorAll('input[id^="score-"]');
    scoreInputs.forEach(input => {
        const score = parseFloat(input.value) || 0;
        total += score;
    });
    document.getElementById('total-score').textContent = total;
}

function getQuestionData() {
    const questions = [];
    const questionElements = document.querySelectorAll('.question');

    questionElements.forEach((questionDiv, index) => {
        const type = document.getElementById(`type-${index}`).value;
        const score = parseFloat(document.getElementById(`score-${index}`).value) || 0;
        const description = document.getElementById(`desc-${index}`).value;
        const answer = document.getElementById(`answer-${index}`).value;

        const questionData = {
            type: type,
            score: score,
            content: description,
            answer: answer
        };

        if (type === 'single' || type === 'multiple') {
            const options = [];
            const optionElements = questionDiv.querySelectorAll('.option-item');
            optionElements.forEach(option => {
                const optionText = option.querySelector('.option-text').value;
                options.push(optionText);
            });
            questionData.options = options;
        }

        questions.push(questionData);
    });

    return questions;
}

layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;

    // 初始化表单
    form.render();

    // 保存为草稿
    window.saveAsDraft = function() {
        saveHomework('draft');
    }

    // 发布作业
    window.publishHomework = function() {
        saveHomework('published');
    }

    // 保存作业
    function saveHomework(status) {
        var title = $('#homework-title').val();
        var description = $('#homework-description').val();
        var deadline = $('#deadline').val();
        var questions = getQuestionData();

        if (!title) {
            layer.msg('请输入作业标题', {icon: 2});
            return;
        }

        if (!deadline) {
            layer.msg('请设置截止时间', {icon: 2});
            return;
        }

        if (questions.length === 0) {
            layer.msg('请至少添加一道题目', {icon: 2});
            return;
        }

        var data = {
            title: title,
            description: description,
            deadline: deadline,
            questions: questions,
            course_id: '{{ current_class.id }}',
            status: status,
            total_score: parseFloat($('#total-score').text())
        };

        $.ajax({
            url: '/teacher/save_homework',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function(res) {
                if (res.status === 'success') {
                    layer.msg('保存成功', {icon: 1});
                    setTimeout(function() {
                        window.location.href = '/teacher/homework';
                    }, 1500);
                } else {
                    layer.msg(res.message || '保存失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
    }
});


//取消
function cancelHomework() {
    layer.confirm('作业尚未保存，确定要取消吗？',{
        title: '提示',
        btn: ['确定', '继续编辑'],
        icon: 3
    }, function(index){
        layer.close(index);//点击确定
        window.location.href = '/teacher/homework';
    }, function(index){
        layer.close(index);//点击继续编辑
    });
}

// 导入习题相关变量
let importExercises = [];
let selectedExercises = [];
let importCurrentFilters = {
    keyword: '',
    types: [],
    difficulties: []
};

// 打开导入习题弹窗
function openImportExerciseModal() {
    layer.open({
        type: 1,
        title: '从资源库导入习题',
        content: $('#importExerciseModal'),
        area: ['50%', '80%'],
        success: function() {
            // 重置选择状态
            selectedExercises = [];
            importCurrentFilters = {
                keyword: '',
                types: [],
                difficulties: []
            };

            // 清空搜索框
            $('#importSearchInput').val('');
            $('#importClearSearchBtn').hide();

            // 重置筛选条件
            $('input[name="importTypeFilter"]').prop('checked', false);
            $('input[name="importDifficultyFilter"]').prop('checked', false);
            $('#importFilterText').text('全部');

            // 加载习题列表
            loadImportExercises();

            // 绑定事件
            bindImportEvents();
        }
    });
}

// 绑定导入弹窗事件
function bindImportEvents() {
    // 搜索功能
    $('#importSearchBtn').off('click').on('click', function() {
        importCurrentFilters.keyword = $('#importSearchInput').val().trim();
        loadImportExercises();
    });

    $('#importSearchInput').off('keypress').on('keypress', function(e) {
        if (e.which === 13) {
            $('#importSearchBtn').click();
        }
    });

    $('#importSearchInput').off('input').on('input', function() {
        const value = $(this).val();
        $('#importClearSearchBtn').toggle(value.length > 0);
    });

    $('#importClearSearchBtn').off('click').on('click', function() {
        $('#importSearchInput').val('');
        $(this).hide();
        importCurrentFilters.keyword = '';
        loadImportExercises();
    });

    // 筛选功能
    $('#importFilterBtn').off('click').on('click', function(e) {
        e.stopPropagation();
        $('#importFilterPanel').toggle();
    });

    $(document).off('click.importFilter').on('click.importFilter', function(e) {
        if (!$(e.target).closest('#importFilterPanel, #importFilterBtn').length) {
            $('#importFilterPanel').hide();
        }
    });

    $('#importClearFilterBtn').off('click').on('click', function() {
        $('input[name="importTypeFilter"]').prop('checked', false);
        $('input[name="importDifficultyFilter"]').prop('checked', false);
        $('#importFilterText').text('全部');
    });

    $('#importConfirmFilterBtn').off('click').on('click', function() {
        // 获取选中的题型
        const selectedTypes = [];
        $('input[name="importTypeFilter"]:checked').each(function() {
            selectedTypes.push($(this).val());
        });

        // 获取选中的难度
        const selectedDifficulties = [];
        $('input[name="importDifficultyFilter"]:checked').each(function() {
            selectedDifficulties.push($(this).val());
        });

        importCurrentFilters.types = selectedTypes;
        importCurrentFilters.difficulties = selectedDifficulties;

        // 更新筛选文本
        updateImportFilterText();

        // 隐藏筛选面板
        $('#importFilterPanel').hide();

        // 重新加载数据
        loadImportExercises();
    });

    // 全选功能
    $('#selectAllExercises').off('change').on('change', function() {
        const isChecked = $(this).prop('checked');
        $('.exercise-checkbox').prop('checked', isChecked);

        if (isChecked) {
            selectedExercises = [...importExercises];
        } else {
            selectedExercises = [];
        }

        updateImportButtonState();
    });

    // 确认导入
    $('#confirmImportBtn').off('click').on('click', function() {
        if (selectedExercises.length === 0) {
            layer.msg('请选择要导入的习题', {icon: 2});
            return;
        }

        // 导入选中的习题
        importSelectedExercises();
        layer.closeAll();
    });
}

// 加载导入习题列表
function loadImportExercises() {
    const params = new URLSearchParams();

    if (importCurrentFilters.keyword) {
        params.append('keyword', importCurrentFilters.keyword);
    }

    if (importCurrentFilters.types.length > 0) {
        params.append('types', importCurrentFilters.types.join(','));
    }

    if (importCurrentFilters.difficulties.length > 0) {
        params.append('difficulties', importCurrentFilters.difficulties.join(','));
    }

    fetch(`/dashboard/get_exercises?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                importExercises = data.exercises;
                renderImportExerciseList();
                $('#importQuestionCountText').text(`共 ${importExercises.length} 道题`);
            } else {
                layer.msg('加载习题失败: ' + data.message, {icon: 2});
            }
        })
        .catch(error => {
            layer.msg('请求失败: ' + error, {icon: 2});
        });
}

// 渲染导入习题列表
function renderImportExerciseList() {
    const tbody = $('#importExerciseTableBody');

    if (importExercises.length === 0) {
        tbody.html(`
            <tr>
                <td colspan="6" style="text-align: center; color: #999; padding: 40px;">
                    暂无符合条件的习题
                </td>
            </tr>
        `);
        return;
    }

    let html = '';
    importExercises.forEach((exercise, index) => {
        const typeMap = {
            'single': '单选题',
            'multiple': '多选题',
            'judge': '判断题',
            'fill': '填空题',
            'subjective': '主观题'
        };

        const difficultyMap = {
            'easy': '简单',
            'medium': '中等',
            'hard': '困难'
        };

        const typeClass = `type-${exercise.type}`;
        const difficultyClass = `difficulty-${exercise.difficulty}`;

        html += `
            <tr>
                <td>
                    <input type="checkbox" class="exercise-checkbox" data-exercise-id="${exercise.id}" lay-skin="primary">
                </td>
                <td>${index + 1}</td>
                <td>
                    <span class="layui-badge ${typeClass}">${typeMap[exercise.type] || exercise.type}</span>
                </td>
                <td style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="${exercise.question}">
                    ${exercise.question}
                </td>
                <td>
                    <span class="layui-badge ${difficultyClass}">${difficultyMap[exercise.difficulty] || exercise.difficulty}</span>
                </td>
                <td>${new Date(exercise.created_at).toLocaleDateString()}</td>
            </tr>
        `;
    });

    tbody.html(html);

    // 绑定复选框事件
    $('.exercise-checkbox').off('change').on('change', function() {
        const exerciseId = $(this).data('exercise-id');
        const exercise = importExercises.find(ex => ex.id === exerciseId);

        if ($(this).prop('checked')) {
            if (!selectedExercises.find(ex => ex.id === exerciseId)) {
                selectedExercises.push(exercise);
            }
        } else {
            selectedExercises = selectedExercises.filter(ex => ex.id !== exerciseId);
        }

        updateImportButtonState();
        updateSelectAllState();
    });
}

// 更新筛选文本
function updateImportFilterText() {
    const typeCount = importCurrentFilters.types.length;
    const difficultyCount = importCurrentFilters.difficulties.length;

    if (typeCount === 0 && difficultyCount === 0) {
        $('#importFilterText').text('全部');
    } else {
        const parts = [];
        if (typeCount > 0) {
            parts.push(`${typeCount}种题型`);
        }
        if (difficultyCount > 0) {
            parts.push(`${difficultyCount}种难度`);
        }
        $('#importFilterText').text(parts.join(', '));
    }
}

// 更新导入按钮状态
function updateImportButtonState() {
    const btn = $('#confirmImportBtn');
    if (selectedExercises.length > 0) {
        btn.removeClass('layui-btn-disabled').prop('disabled', false);
        btn.html(`<i class="layui-icon layui-icon-ok"></i> 导入选中习题 (${selectedExercises.length})`);
    } else {
        btn.addClass('layui-btn-disabled').prop('disabled', true);
        btn.html('<i class="layui-icon layui-icon-ok"></i> 导入选中习题');
    }
}

// 更新全选状态
function updateSelectAllState() {
    const totalCheckboxes = $('.exercise-checkbox').length;
    const checkedCheckboxes = $('.exercise-checkbox:checked').length;

    if (checkedCheckboxes === 0) {
        $('#selectAllExercises').prop('checked', false).prop('indeterminate', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
        $('#selectAllExercises').prop('checked', true).prop('indeterminate', false);
    } else {
        $('#selectAllExercises').prop('checked', false).prop('indeterminate', true);
    }
}

// 导入选中的习题
function importSelectedExercises() {
    selectedExercises.forEach(exercise => {
        // 将习题数据转换为作业题目格式
        const questionData = convertExerciseToQuestion(exercise);
        addQuestionFromData(questionData);
    });

    layer.msg(`成功导入 ${selectedExercises.length} 道习题`, {icon: 1});
    calculateTotalScore();
}

// 将习题数据转换为作业题目格式
function convertExerciseToQuestion(exercise) {
    const questionData = {
        type: exercise.type,
        question: exercise.question,
        score: 5 // 默认分值
    };

    // 根据题型处理选项和答案
    switch (exercise.type) {
        case 'single':
        case 'multiple':
            if (exercise.options) {
                // 检查选项数据是否已经是数组格式
                if (Array.isArray(exercise.options)) {
                    questionData.options = exercise.options;
                } else {
                    // 如果是字符串，尝试解析JSON
                    try {
                        questionData.options = JSON.parse(exercise.options);
                    } catch (e) {
                        console.warn('选项数据解析失败，使用默认选项:', e);
                        questionData.options = ['选项A', '选项B', '选项C', '选项D'];
                    }
                }
            } else {
                questionData.options = ['选项A', '选项B', '选项C', '选项D'];
            }
            questionData.answer = exercise.answer;
            break;

        case 'judge':
            questionData.answer = exercise.answer;
            break;

        case 'fill':
        case 'subjective':
            questionData.answer = exercise.answer;
            break;
    }

    return questionData;
}

// 从数据添加题目到作业
function addQuestionFromData(questionData) {
    const questionsDiv = document.getElementById("questions");
    const noQuestionsMessage = document.getElementById("no-questions-message");

    // 如果存在提示文字，则移除
    if (noQuestionsMessage) {
        noQuestionsMessage.remove();
    }

    const questionDiv = document.createElement("div");
    questionDiv.className = "layui-form-item question";

    // 动态计算当前题目数量作为新题目的序号
    const currentQuestionCount = questionsDiv.querySelectorAll('.question').length;
    questionDiv.id = `question-${currentQuestionCount}`;

    // 使用与原有addQuestion函数相同的HTML结构
    let html = `
        <input type="hidden" id="type-${currentQuestionCount}" value="${questionData.type}">
        <div class="question-score">
            <div class="question-score-left">
                <label>分值：</label>
                <input type="number" id="score-${currentQuestionCount}" lay-verify="required|number" min="1" value="${questionData.score || 5}" autocomplete="off" class="layui-input">
            </div>
            <button type="button" class="delete-btn" onclick="removeQuestion(${currentQuestionCount})">删除题目</button>
        </div>
        <div class="layui-form-item question-header">
            <span class="question-number">${currentQuestionCount + 1}.</span>
            <input type="text" id="desc-${currentQuestionCount}" lay-verify="required" placeholder="请输入题目" autocomplete="off" class="layui-input" value="${questionData.question}">
        </div>
    `;

    // 根据题型生成选项部分
    if (questionData.type === "single" || questionData.type === "multiple") {
        html += generateOptionsHTML(currentQuestionCount, questionData);
    } else if (questionData.type === "judge") {
        html += generateJudgeOptionsHTML(currentQuestionCount, questionData);
    } else if (questionData.type === "fill") {
        html += generateFillAnswerHTML(currentQuestionCount, questionData);
    } else if (questionData.type === "subjective") {
        html += generateSubjectiveAnswerHTML(currentQuestionCount, questionData);
    }

    questionDiv.innerHTML = html;
    questionsDiv.appendChild(questionDiv);

    // 为分值输入框绑定 input 事件，实时更新总分
    const scoreInput = document.getElementById(`score-${currentQuestionCount}`);
    scoreInput.addEventListener('input', calculateTotalScore);

    // 为选项添加事件监听（复用原有逻辑）
    addEventListenersToQuestion(questionDiv, currentQuestionCount, questionData.type);

    // 重新计算题目序号和总分
    updateQuestionNumbers();
    calculateTotalScore();

    // 更新全局题目计数
    questionCount++;
}

// 生成选择题选项HTML
function generateOptionsHTML(questionIndex, questionData) {
    const options = questionData.options || ['选项A', '选项B', '选项C', '选项D'];
    const type = questionData.type;
    const inputType = type === "single" ? "radio" : "checkbox";
    const answers = questionData.answer ? (type === "multiple" ? questionData.answer.split(',') : [questionData.answer]) : [];

    let html = `<div class="options-container" id="options-container-${questionIndex}">`;

    options.forEach((option, index) => {
        const letter = String.fromCharCode(65 + index);
        const isCorrect = answers.includes(option) || answers.includes(letter);

        html += `
            <div class="option-item ${isCorrect ? 'correct' : ''}" data-index="${index}">
                <div class="option-content">
                    <input type="${inputType}" name="option-${questionIndex}" ${isCorrect ? 'checked' : ''} class="option-checkbox" title="正确答案">
                    <span class="option-letter">${letter}.</span>
                    <input type="text" class="layui-input option-text" placeholder="请输入选项内容" value="${option}">
                    ${isCorrect ? '<span class="correct-answer">正确答案</span>' : ''}
                </div>
                <button type="button" class="option-delete-btn" onclick="deleteOption(${questionIndex}, ${index})" title="删除选项"></button>
            </div>
        `;
    });

    html += `
        <div class="add-option">
            <button type="button" class="add-option-btn" onclick="addOption(${questionIndex})">
                <i class="layui-icon layui-icon-add-1"></i>
                添加选项
            </button>
        </div>
    </div>
    <input type="hidden" id="answer-${questionIndex}" value="${questionData.answer || (type === 'single' ? 'A' : '')}">
    `;

    return html;
}

// 生成判断题选项HTML
function generateJudgeOptionsHTML(questionIndex, questionData) {
    const answer = questionData.answer || '正确';

    let html = `
        <div class="options-container">
            <div class="option-item judge-item ${answer === '正确' ? 'correct' : ''}" data-value="正确">
                <div class="option-content">
                    <input type="radio" name="option-${questionIndex}" ${answer === '正确' ? 'checked' : ''} class="option-checkbox" title="正确答案">
                    <span class="option-letter">A.</span>
                    <span class="fixed-option">正确</span>
                    ${answer === '正确' ? '<span class="correct-answer">正确答案</span>' : ''}
                </div>
            </div>
            <div class="option-item judge-item ${answer === '错误' ? 'correct' : ''}" data-value="错误">
                <div class="option-content">
                    <input type="radio" name="option-${questionIndex}" ${answer === '错误' ? 'checked' : ''} class="option-checkbox" title="错误">
                    <span class="option-letter">B.</span>
                    <span class="fixed-option">错误</span>
                    ${answer === '错误' ? '<span class="correct-answer">正确答案</span>' : ''}
                </div>
            </div>
        </div>
        <input type="hidden" id="answer-${questionIndex}" value="${answer}">
    `;

    return html;
}

// 生成填空题答案HTML
function generateFillAnswerHTML(questionIndex, questionData) {
    return `
        <div class="layui-form-item fill-answer">
            <label class="layui-form-label">填空题答案：</label>
            <div class="layui-input-block">
                <input type="text" id="answer-${questionIndex}" lay-verify="required" autocomplete="off" class="layui-input" value="${questionData.answer || ''}">
            </div>
        </div>
    `;
}

// 生成主观题答案HTML
function generateSubjectiveAnswerHTML(questionIndex, questionData) {
    return `
        <div class="layui-form-item subjective-answer">
            <label class="layui-form-label">主观题答案：</label>
            <div class="layui-input-block">
                <textarea id="answer-${questionIndex}" lay-verify="required" class="layui-textarea" rows="4">${questionData.answer || ''}</textarea>
            </div>
        </div>
    `;
}

// 为导入的题目添加事件监听（复用原有逻辑）
function addEventListenersToQuestion(questionDiv, questionIndex, type) {
    if (type === "single" || type === "multiple") {
        const optionItems = questionDiv.querySelectorAll('.option-item');
        optionItems.forEach((item, index) => {
            const checkbox = item.querySelector('.option-checkbox');

            // 绑定整个选项项的点击事件
            item.addEventListener('click', function(e) {
                // 如果点击的是删除按钮或文本输入框，不处理
                if (e.target.classList.contains('option-delete-btn') ||
                    e.target.closest('.option-delete-btn') ||
                    e.target.classList.contains('option-text')) {
                    return;
                }

                // 阻止事件冒泡
                e.preventDefault();
                e.stopPropagation();

                if (type === "single") {
                    // 单选：移除所有正确答案标识
                    optionItems.forEach(optionItem => {
                        optionItem.querySelector('.option-checkbox').checked = false;
                        optionItem.classList.remove('correct');
                        const correctSpan = optionItem.querySelector('.correct-answer');
                        if (correctSpan) correctSpan.remove();
                    });

                    // 设置当前为正确答案
                    checkbox.checked = true;
                    item.classList.add('correct');
                    if (!item.querySelector('.correct-answer')) {
                        const correctSpan = document.createElement('span');
                        correctSpan.className = 'correct-answer';
                        correctSpan.textContent = '正确答案';
                        item.querySelector('.option-content').appendChild(correctSpan);
                    }

                    // 更新答案值
                    document.getElementById(`answer-${questionIndex}`).value = String.fromCharCode(65 + index);
                } else if (type === "multiple") {
                    // 多选：切换选中状态
                    const isChecked = checkbox.checked;
                    checkbox.checked = !isChecked;

                    if (!isChecked) {
                        item.classList.add('correct');
                        if (!item.querySelector('.correct-answer')) {
                            const correctSpan = document.createElement('span');
                            correctSpan.className = 'correct-answer';
                            correctSpan.textContent = '正确答案';
                            item.querySelector('.option-content').appendChild(correctSpan);
                        }
                    } else {
                        item.classList.remove('correct');
                        const correctSpan = item.querySelector('.correct-answer');
                        if (correctSpan) correctSpan.remove();
                    }

                    // 更新多选答案，收集所有选中的选项
                    const selectedOptions = [];
                    optionItems.forEach((optionItem, idx) => {
                        if (optionItem.querySelector('.option-checkbox').checked) {
                            selectedOptions.push(String.fromCharCode(65 + idx));
                        }
                    });
                    document.getElementById(`answer-${questionIndex}`).value = selectedOptions.join(',');
                }
            });

            // 单独绑定复选框的点击事件
            checkbox.addEventListener('click', function(e) {
                e.stopPropagation();
                // 触发父级的点击事件
                item.click();
            });

            // 阻止文本框的点击事件冒泡
            const optionText = item.querySelector('.option-text');
            if (optionText) {
                optionText.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            }
        });
    } else if (type === "judge") {
        // 为判断题添加事件监听
        const judgeItems = questionDiv.querySelectorAll('.judge-item');
        judgeItems.forEach((item) => {
            const radio = item.querySelector('.option-checkbox');
            const answer = item.getAttribute('data-value');

            item.addEventListener('click', function(e) {
                if (e.target.closest('.judge-item')) {
                    // 移除所有正确答案标识
                    judgeItems.forEach(judgeItem => {
                        judgeItem.querySelector('.option-checkbox').checked = false;
                        judgeItem.classList.remove('correct');
                        const correctSpan = judgeItem.querySelector('.correct-answer');
                        if (correctSpan) correctSpan.remove();
                    });

                    // 设置当前为正确答案
                    radio.checked = true;
                    item.classList.add('correct');
                    if (!item.querySelector('.correct-answer')) {
                        const correctSpan = document.createElement('span');
                        correctSpan.className = 'correct-answer';
                        correctSpan.textContent = '正确答案';
                        item.querySelector('.option-content').appendChild(correctSpan);
                    }

                    // 更新答案值
                    document.getElementById(`answer-${questionIndex}`).value = answer;
                }
            });
        });
    }
}
</script>
{% endblock %}
