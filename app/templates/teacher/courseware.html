{% extends "teacher/base.html" %}

{% block title %}智慧课堂系统 - 课件管理{% endblock %}

{% block styles %}
<style>

    .no-materials {
        text-align: center;
        padding: 40px;
        color: #999;
    }
    .current-class-header {
        background: linear-gradient(135deg, #1e9fff 0%, #0084ff 100%);
        color: white;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    .class-info {
        font-size: 16px;
        margin-bottom: 5px;
    }
</style>
{% endblock %}

{% block content %}
{% if current_class %}
<div class="layui-card">
    <div class="layui-card-header">
        <i class="layui-icon layui-icon-file"></i> 课件
        <div style="float: right;">
            <input type="file" id="file-input" multiple accept=".pdf,.doc,.docx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif" style="display: none;">
            <button type="button" class="layui-btn layui-btn-normal layui-btn-sm" id="upload-material-btn">
                <i class="layui-icon">&#xe67c;</i> 选择文件上传
            </button>
            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" id="import-material-btn">
                <i class="layui-icon layui-icon-download-circle"></i> 导入课件
            </button>
        </div>
    </div>
    <div class="layui-card-body">
        {% if course_materials %}
        <table class="layui-table">
            <colgroup>
                <col>
                <col width="100">
                <col width="100">
                <col width="180">
                <col width="200">
            </colgroup>
            <thead>
                <tr>
                    <th>课件标题</th>
                    <th>类型</th>
                    <th>来源</th>
                    <th>上传时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for material in course_materials %}
                <tr>
                    <td>
                        {{ material.title }}
                        {% if material.description %}
                        <div style="font-size: 12px; color: #999;">描述：{{ material.description }}</div>
                        {% endif %}
                    </td>
                    <td><span class="layui-badge layui-bg-gray">{{ material.file_type }}</span></td>
                    <td>
                        {% if material.source == 'course_material' %}
                        <span class="layui-badge layui-bg-blue">课堂上传</span>
                        {% else %}
                        <span class="layui-badge layui-bg-green">资源库</span>
                        {% endif %}
                    </td>
                    <td>{{ material.upload_time[:19] }}</td>
                    <td>
                        <div class="layui-btn-group">
                            <button class="layui-btn layui-btn-xs layui-btn-primary view-material-btn" data-path="{{ material.file_path }}" data-title="{{ material.title }}">
                                <i class="layui-icon layui-icon-search"></i> 查看
                            </button>
                            <button class="layui-btn layui-btn-xs layui-btn-normal" onclick="window.open('{{ material.file_path }}', '_blank')">
                                <i class="layui-icon layui-icon-download-circle"></i> 下载
                            </button>
                            {% if material.source == 'course_material' %}
                            <button class="layui-btn layui-btn-xs layui-btn-danger delete-material-btn" data-id="{{ material.id }}">
                                <i class="layui-icon layui-icon-delete"></i> 删除
                            </button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <div class="no-materials">
            <i class="layui-icon layui-icon-file" style="font-size: 48px; color: #d9d9d9;"></i>
            <p style="font-size: 16px; margin-top: 10px;">暂无课件</p>
            <p style="font-size: 14px; color: #ccc;">点击上传课件或从资源库导入</p>
        </div>
        {% endif %}
    </div>
</div>



<!-- 导入课件弹窗 -->
<div id="import-modal" style="display: none; padding: 20px;">
    <div class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label">选择课件</label>
            <div class="layui-input-block">
                <div id="resource-materials-list" style="max-height: 300px; overflow-y: auto;">
                    <!-- 动态加载资源库课件列表 -->
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="button" class="layui-btn" id="confirm-import-btn">
                    <i class="layui-icon layui-icon-download-circle"></i> 导入选中
                </button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </div>
    </div>
</div>

{% else %}
<div style="text-align: center; padding: 60px; color: #999;">
    <i class="layui-icon layui-icon-tips" style="font-size: 48px; color: #d9d9d9;"></i>
    <p style="font-size: 18px; margin-top: 15px;">请先在首页选择要管理的课堂</p>
    <button class="layui-btn layui-btn-normal" onclick="location.href='/teacher'">
        <i class="layui-icon layui-icon-return"></i> 返回首页
    </button>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
layui.use(['layer', 'form', 'upload'], function(){
    var layer = layui.layer;
    var form = layui.form;
    var upload = layui.upload;

    {% if current_class %}
    // 上传课件按钮 - 直接触发文件选择
    $('#upload-material-btn').click(function() {
        $('#file-input').click();
    });

    // 文件选择后自动上传
    $('#file-input').change(function() {
        var files = this.files;
        if (files.length === 0) {
            return;
        }

        var formData = new FormData();
        for (var i = 0; i < files.length; i++) {
            formData.append('files', files[i]);
        }
        formData.append('folder_path', '/');

        // 显示上传进度
        var loadingIndex = layer.load(1, {
            shade: [0.3, '#000']
        });

        $.ajax({
            url: '/teacher/upload_course_materials/{{ current_class.id }}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(res) {
                layer.close(loadingIndex);
                if (res.status === 'success') {
                    layer.msg('课件上传成功', {icon: 1});
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    layer.msg('上传失败：' + res.message, {icon: 2});
                }
            },
            error: function() {
                layer.close(loadingIndex);
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });

        // 清空文件选择器
        this.value = '';
    });

    // 导入课件按钮
    $('#import-material-btn').click(function() {
        // 先加载资源库课件列表
        loadResourceMaterials();
        
        layer.open({
            type: 1,
            title: '从资源库导入课件',
            content: $('#import-modal'),
            area: ['600px', '500px'],
            btn: false,
            shadeClose: true
        });
    });

    // 加载资源库课件列表
    function loadResourceMaterials() {
        $.ajax({
            url: '/teacher/get_resource_materials',
            type: 'GET',
            success: function(res) {
                if (res.status === 'success') {
                    renderResourceMaterials(res.materials);
                } else {
                    $('#resource-materials-list').html('<p style="text-align: center; color: #999;">加载失败</p>');
                }
            },
            error: function() {
                $('#resource-materials-list').html('<p style="text-align: center; color: #999;">网络错误</p>');
            }
        });
    }

    // 渲染资源库课件列表
    function renderResourceMaterials(materials) {
        var html = '';
        if (materials.length === 0) {
            html = '<p style="text-align: center; color: #999;">资源库中暂无课件</p>';
        } else {
            materials.forEach(function(material) {
                html += `
                    <div class="layui-form-item">
                        <input type="checkbox" name="material_ids" value="${material.id}" title="${material.title}" lay-skin="primary">
                        <div style="margin-left: 30px; font-size: 12px; color: #999;">
                            类型：${material.file_type} | 上传时间：${material.upload_time.substring(0, 19)}
                        </div>
                    </div>
                `;
            });
        }
        $('#resource-materials-list').html(html);
        form.render('checkbox');
    }

    // 确认导入按钮
    $('#confirm-import-btn').click(function() {
        var selectedIds = [];
        $('input[name="material_ids"]:checked').each(function() {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length === 0) {
            layer.msg('请选择要导入的课件', {icon: 2});
            return;
        }

        $.ajax({
            url: '/teacher/import_materials/{{ current_class.id }}',
            type: 'POST',
            data: {material_ids: selectedIds.join(',')},
            success: function(res) {
                if (res.status === 'success') {
                    layer.msg('课件导入成功', {icon: 1});
                    layer.closeAll();
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    layer.msg('导入失败：' + res.message, {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
    });

    // 查看课件
    $(document).on('click', '.view-material-btn', function() {
        var filePath = $(this).data('path');
        var title = $(this).data('title');

        // 根据文件类型决定查看方式
        var fileExt = filePath.split('.').pop().toLowerCase();

        if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(fileExt)) {
            // 图片文件 - 在弹窗中显示
            layer.photos({
                photos: {
                    title: title,
                    data: [{
                        alt: title,
                        pid: 1,
                        src: filePath,
                        thumb: filePath
                    }]
                },
                anim: 5
            });
        } else if (['pdf'].includes(fileExt)) {
            // PDF文件 - 在新窗口中打开
            window.open(filePath, '_blank');
        } else {
            // 其他文件类型 - 提示下载
            layer.confirm('该文件类型不支持在线预览，是否下载查看？', function(index) {
                window.open(filePath, '_blank');
                layer.close(index);
            });
        }
    });

    // 删除课件
    $(document).on('click', '.delete-material-btn', function() {
        var materialId = $(this).data('id');

        layer.confirm('确定要删除这个课件吗？', function(index) {
            $.ajax({
                url: '/teacher/delete_course_material/' + materialId,
                type: 'DELETE',
                success: function(res) {
                    if (res.status === 'success') {
                        layer.msg('删除成功', {icon: 1});
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        layer.msg('删除失败：' + res.message, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('网络错误，请重试', {icon: 2});
                }
            });
            layer.close(index);
        });
    });
    {% endif %}
});
</script>
{% endblock %}
