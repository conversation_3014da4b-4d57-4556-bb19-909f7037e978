<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}智慧课堂系统{% endblock %}</title>
    <link rel="stylesheet" href="/static/css/layui.css">
    {% block styles %}{% endblock %}
    <style>

        /* 顶部导航栏样式 */
        .layui-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background-color: #393D49;
            color: #fff;
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .layui-nav {
            display: flex;
            align-items: center;
            background: none;
            margin-left: 30px;
            flex-grow: 1;
        }

        .layui-nav .layui-nav-item {
            line-height: 60px;
            margin: 0 5px;
        }

        .layui-nav .layui-nav-item a {
            color: #fff;
            padding: 0 15px;
            transition: all 0.3s;
        }

        .layui-nav .layui-nav-item a:hover {
            color: #1E9FFF;
        }

        .layui-nav .layui-this:after {
            background-color: #1E9FFF;
        }

        .layui-nav .layui-nav-item i {
            margin-right: 5px;
            font-size: 16px;
        }

        /* 左侧课堂信息栏样式 */
        .layui-side {
            width: 280px;
            background: #393D49;
        }

        .current-class-sidebar {
            background: #393D49;
            color: #fff;
            height: 100vh;
            overflow-y: auto;
        }

        .class-info-label {
            color: #C2C2C2;
            display: inline-block;
            width: 50px;
        }

        .class-info-value {
            color: #fff;
        }

        .class-status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 5px;
        }

        .status-scheduled {
            background: #FFB800;
            color: #fff;
        }

        .status-in_progress {
            background: #5FB878;
            color: #fff;
        }

        .status-completed {
            background: #999;
            color: #fff;
        }

        .no-class-info {
            text-align: center;
            color: #999;
            padding: 20px;
            font-size: 14px;
        }

        .status-scheduled {
            background: #FFB800;
            color: #fff;
        }

        .status-in_progress {
            background: #5FB878;
            color: #fff;
        }

        .status-completed {
            background: #999;
            color: #fff;
        }

        /* 主体内容区域 */
        .layui-body {
            left: 200px;
        }
        
        /* 主题内容区域上边距 */
        .layui-container {
            padding-top: 75px !important;
            width: 100%;
            max-width: 100%; 
            padding: 15px 15px 15px;
        }

        /* 右侧悬浮菜单 */
        .floating-menu {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 999;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 10px 0;
            width: 50px;
            transition: all 0.3s ease;
        }

        .floating-menu.hidden {
            right: -40px;
            width: 20px;
            overflow: visible;
        }

        /* 隐藏状态下的触发区域 */
        .floating-menu.hidden::after {
            content: '»';
            position: absolute;
            left: -30px;
            top: 50%;
            transform: translateY(-50%);
            width: 25px;
            height: 40px;
            background: #1E9FFF;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 5px 0 0 5px;
            cursor: pointer;
        }
        
        /* 菜单控制按钮样式 */
        .menu-toggle-item {
            display: flex;
            align-items: center;
            justify-content: center;
            background: #1E9FFF;
            color: #fff;
            width: 100%;
            height: 30px;
            cursor: pointer;
            border-radius: 8px 8px 0 0;
            margin-top: -10px;
            margin-bottom: 5px;
        }
        
        .menu-toggle-item i {
            font-size: 16px;
            transition: transform 0.3s ease;
        }
        
        .menu-toggle-item.collapsed i {
            transform: rotate(180deg);
        }

        .floating-menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 10px 0;
            color: #666;
            cursor: pointer;
            text-decoration: none;
            position: relative;
            transition: all 0.3s ease;
        }

        .floating-menu-item:hover {
            color: #1E9FFF;
            background: #f5f5f5;
        }

        .floating-menu-item.active {
            color: #1E9FFF;
        }

        .floating-menu-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .floating-menu-item span {
            font-size: 12px;
            text-align: center;
            display: none;
            position: absolute;
            right: 60px;
            background: #333;
            color: #fff;
            padding: 5px 10px;
            border-radius: 4px;
            white-space: nowrap;
        }

        .floating-menu-item:hover span {
            display: block;
        }

        /* 分隔线 */
        .floating-menu-divider {
            height: 1px;
            background: rgba(255,255,255,0.1);
            margin: 10px 8px;
        }

        /* 课堂信息悬浮卡片 */
        .class-info-popup {
            display: none;
            position: fixed;
            width: 300px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.15);
            padding: 15px;
            z-index: 1000;
            max-height: 90vh;
            overflow-y: auto;
        }

        .class-info-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .class-info-item {
            margin-bottom: 10px;
            font-size: 14px;
            display: flex;
        }

        .class-info-label {
            color: #666;
            width: 60px;
            flex-shrink: 0;
        }

        .class-info-value {
            color: #333;
            flex-grow: 1;
        }

        @media screen and (max-width: 768px) {
            .floating-menu {
                width: 40px;
                padding: 5px 0;
            }
            .menu-toggle-item {
                height: 25px;
            }
            .floating-menu-item i {
                font-size: 16px;
            }
            .layui-body {
                padding-right: 15px;
            }
        }
    </style>
</head>
<body class="layui-layout-body">
    <div class="layui-layout">
        <!-- 顶部导航栏 -->
        <div class="layui-header">
            <div class="layui-logo">
                <div class="logo-icon"></div>
                <span>智慧课堂系统</span>
            </div>
            <ul class="layui-nav">
                <li class="layui-nav-item{% if current_page == 'index' %} layui-this{% endif %}">
                    <a href="/teacher">
                        <i class="layui-icon layui-icon-home"></i> 课堂
                    </a>
                </li>
                <li class="layui-nav-item{% if current_page == 'courseware' %} layui-this{% endif %}">
                    <a href="/teacher/courseware">
                        <i class="layui-icon layui-icon-list"></i> 课件
                    </a>
                </li>
                <li class="layui-nav-item{% if current_page == 'attendance' %} layui-this{% endif %}">
                    <a href="/teacher/attendance">
                        <i class="layui-icon layui-icon-user"></i> 签到
                    </a>
                </li>
                <li class="layui-nav-item{% if current_page == 'groups' %} layui-this{% endif %}">
                    <a href="/teacher/groups">
                        <i class="layui-icon layui-icon-group"></i> 小组
                    </a>
                </li>
                <li class="layui-nav-item{% if current_page == 'homework' %} layui-this{% endif %}">
                    <a href="/teacher/homework">
                        <i class="layui-icon layui-icon-survey"></i> 作业
                    </a>
                </li>
                <li class="layui-nav-item{% if current_page == 'reports' %} layui-this{% endif %}">
                    <a href="/teacher/reports">
                        <i class="layui-icon layui-icon-chart"></i> 报告
                    </a>
                </li>
            </ul>
        </div>

        <!-- 内容区域 -->
        <div class="layui-container">
            {% block content %}{% endblock %}
        </div>

        <!-- 右侧悬浮菜单 -->
        <div class="floating-menu" id="floatingMenu">            
            <!-- 菜单控制按钮 -->
            <div class="menu-toggle-item" id="menuToggleBtn">
                <i class="layui-icon layui-icon-right"></i>
            </div>
            {% if current_class %}
            <a href="javascript:;" class="floating-menu-item" id="classInfoBtn">
                <i class="layui-icon layui-icon-about"></i>
                <span>课堂信息</span>
            </a>
            <a href="javascript:;" class="floating-menu-item" id="randomStudentBtn">
                <i class="layui-icon layui-icon-refresh-3"></i>
                <span>随机点名</span>
            </a>
            {% endif %}
            <div class="floating-menu-divider"></div>
            {% if current_class and current_class.status == 'in_progress' %}
            <a href="javascript:;" class="floating-menu-item end-class-btn" data-id="{{ current_class.id }}">
                <i class="layui-icon layui-icon-pause"></i>
                <span>结束课程</span>
            </a>
            {% endif %}
            <a href="/dashboard" class="floating-menu-item">
                <i class="layui-icon layui-icon-return"></i>
                <span>返回仪表盘</span>
            </a>
        </div>

        <!-- 脚本引用 -->
        <script src="/static/js/jquery.min.js"></script>
        <script src="/static/js/layui.js"></script>
        <!-- 课堂信息悬浮卡片 -->
        {% if current_class %}
        <div class="class-info-popup">
            <div class="class-info-title">
                <i class="layui-icon layui-icon-about"></i> 课堂信息
            </div>
            <div class="class-info-item">
                <span class="class-info-label">课程：</span>
                <span class="class-info-value">{{ current_class.course_name }}</span>
            </div>
            <div class="class-info-item">
                <span class="class-info-label">代码：</span>
                <span class="class-info-value">{{ current_class.course_code }}</span>
            </div>
            <div class="class-info-item">
                <span class="class-info-label">教室：</span>
                <span class="class-info-value">{{ current_class.classroom_name }}</span>
            </div>
            <div class="class-info-item">
                <span class="class-info-label">班级：</span>
                <span class="class-info-value">{{ current_class.class_name }}</span>
            </div>
            <div class="class-info-item">
                <span class="class-info-label">时间：</span>
                <span class="class-info-value">{{ current_class.day_of_week }}</span>
            </div>
            <div class="class-info-item">
                <span class="class-info-label"></span>
                <span class="class-info-value">{{ current_class.start_time }} - {{ current_class.end_time }}</span>
            </div>
            <div class="class-info-item">
                <span class="class-info-label">状态：</span>
                <span class="class-info-value">
                    {% if current_class.status == 'scheduled' %}
                    <span class="class-status status-scheduled">待上课</span>
                    {% elif current_class.status == 'in_progress' %}
                    <span class="class-status status-in_progress">进行中</span>
                    {% elif current_class.status == 'completed' %}
                    <span class="class-status status-completed">已结束</span>
                    {% endif %}
                </span>
            </div>
        </div>
        {% endif %}

        <script>
        layui.use(['element', 'layer'], function(){
            var element = layui.element;
            var layer = layui.layer;

            // 初始化导航菜单
            element.init();

            // 菜单控制逻辑
            var menuToggleBtn = $('#menuToggleBtn');
            var floatingMenu = $('#floatingMenu');
            var isMenuVisible = true;

            menuToggleBtn.on('click', function() {
                isMenuVisible = !isMenuVisible;
                if (!isMenuVisible) {
                    floatingMenu.addClass('hidden');
                    $(this).addClass('collapsed');
                } else {
                    floatingMenu.removeClass('hidden');
                    $(this).removeClass('collapsed');
                }

                // 添加悬停展开功能
                floatingMenu.off('mouseenter').on('mouseenter', function() {
                    if (isMenuVisible === false) {
                        floatingMenu.removeClass('hidden');
                        menuToggleBtn.removeClass('collapsed');
                        isMenuVisible = true;
                    }
                });
            });

            // 课堂信息按钮悬停效果（右侧浮动菜单）
            $('#classInfoBtn, #headerClassInfoBtn').hover(
                function() {
                    var $popup = $('.class-info-popup');
                    var btnOffset = $(this).offset();
                    var windowWidth = $(window).width();
                    var popupWidth = $popup.outerWidth();
                    var btnWidth = $(this).outerWidth();
                    var top = $(this).attr('id') === 'headerClassInfoBtn' ? btnOffset.top + 60 : btnOffset.top;
                    
                    // 判断右侧空间是否足够
                    var left = btnOffset.left + btnWidth + 10; // 按钮右侧位置加上间距
                    if (left + popupWidth > windowWidth) {
                        // 如果右侧空间不足，则显示在按钮左侧
                        left = btnOffset.left - popupWidth - 10;
                    }
                    
                    $popup.css({
                        top: top + 'px',
                        left: left + 'px'
                    }).fadeIn(200);
                },
                function() {
                    var $popup = $('.class-info-popup');
                    $popup.hover(
                        function() { /* 鼠标在弹窗上时保持显示 */ },
                        function() { $(this).fadeOut(200); }
                    );
                    setTimeout(function() {
                        if (!$popup.is(':hover')) {
                            $popup.fadeOut(200);
                        }
                    }, 300);
                }
            );

            // 随机点名功能
            $(document).on('click', '#randomStudentBtn, #headerRandomStudentBtn', function() {
                var classId = "{{ current_class.id if current_class else '' }}";
                if (!classId) {
                    layer.msg('没有正在进行的课堂', {icon: 5});
                    return;
                }

                var loading = layer.load(1, {
                    shade: [0.1,'#fff']
                });

                $.ajax({
                    url: '/teacher/class/' + classId + '/students',
                    type: 'GET',
                    dataType: 'json',
                    success: function(res) {
                        layer.close(loading);
                        if (res.success && res.students && res.students.length > 0) {
                            var students = res.students;
                            var randomIndex = Math.floor(Math.random() * students.length);
                            var randomStudent = students[randomIndex];
                            
                            layer.alert('随机选中的学生是：<br><strong style="font-size: 20px; color: #1E9FFF;">' + randomStudent.name + '</strong>', {
                                title: '随机点名结果',
                                icon: 6,
                                btn: ['换一个', '确定'],
                                yes: function(index, layero){
                                    layer.close(index);
                                    $('#randomStudentBtn').click();
                                },
                                btn2: function(index, layero){
                                    layer.close(index);
                                }
                            });
                        } else {
                            layer.alert(res.message || '当前课堂没有学生。', {icon: 5});
                        }
                    },
                    error: function() {
                        layer.close(loading);
                        layer.alert('请求学生列表失败。', {icon: 2});
                    }
                });
            });

            // 结束课程功能
            $(document).on('click', '.end-class-btn', function(){
                var courseId = $(this).data('id');

                layer.confirm('确定要结束课程吗？', {
                    icon: 3,
                    title: '提示'
                }, function(index){
                    $.ajax({
                        url: '/teacher/end_class/' + courseId,
                        type: 'POST',
                        success: function(res){
                            if(res.status === 'success'){
                                layer.msg('已结束课程');
                                setTimeout(function() {
                                    location.reload();
                                }, 1000);
                            } else {
                                layer.msg('操作失败: ' + res.message);
                            }
                        },
                        error: function(xhr){
                            layer.msg('请求失败: ' + xhr.responseText);
                        }
                    });

                    layer.close(index);
                });
            });
        });
        </script>
        {% block scripts %}{% endblock %}
    </div>
</body>
</html>