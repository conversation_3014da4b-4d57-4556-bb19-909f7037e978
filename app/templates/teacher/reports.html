{% extends "teacher/base.html" %}

{% block title %}智慧课堂系统 - 课堂报告{% endblock %}

{% block styles %}
<style>
    .report-card {
        margin-bottom: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .report-main-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .report-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 8px 8px 0 0;
    }
    .report-title {
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 10px;
    }
    .report-info {
        font-size: 14px;
        opacity: 0.9;
    }
    .report-content {
        padding: 20px;
    }
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }
    .stat-item {
        text-align: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        /* border-left managed by .accent-border-left */
    }
    .stat-number {
        font-size: 32px;
        font-weight: bold;
        color: #1e9fff;
        margin-bottom: 5px;
    }
    .stat-label {
        font-size: 14px;
        color: #666;
    }
    .section-title {
        font-size: 16px;
        font-weight: bold;
        margin: 20px 0 10px 0;
        color: #333;
        /* border-left managed by .accent-border-left */
        padding-left: 10px;
    }
    .accent-border-left {
        border-left: 4px solid #1e9fff;
    }
    .attendance-list {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    .student-item {
        display: inline-block;
        margin: 5px;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
    }
    .student-present {
        background: #52c41a;
        color: white;
    }
    .student-absent {
        background: #ff4d4f;
        color: white;
    }
    .homework-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
    }
    .homework-title {
        font-weight: bold;
        margin-bottom: 5px;
    }
    .homework-stats {
        font-size: 14px;
        color: #666;
    }
    .remarks-section {
        background: #fff9e6;
        border: 1px solid #ffe58f;
        border-radius: 8px;
        padding: 15px;
        margin-top: 20px;
    }
    .no-class-selected {
        text-align: center;
        padding: 60px;
        color: #999;
    }
    .no-class-selected .layui-icon-tips {
        font-size: 48px;
        color: #d9d9d9;
        margin-bottom: 15px;
    }
    .no-class-selected .no-class-prompt {
        font-size: 18px;
        margin-top: 10px;
        color: #777;
    }
    .no-class-selected .no-class-guidance {
        font-size: 14px;
        color: #aaa;
        margin-top: 8px;
    }
    .no-class-selected .no-class-guidance a {
        color: #1e9fff;
        text-decoration: none;
    }
    .no-class-selected .no-class-guidance a:hover {
        text-decoration: underline;
    }
    .generate-btn {
        margin-bottom: 20px;
    }
    .empty-data-message {
        color: #999;
        text-align: center;
        padding: 15px 0;
    }
</style>
{% endblock %}

{% block content %}
{% if current_class %}
<div class="layui-card">
    <div class="layui-card-header report-main-header">
        <span><i class="layui-icon layui-icon-chart"></i> 报告</span>
        <div>
            <button class="layui-btn layui-btn-sm" id="refreshBtn">
                <i class="layui-icon layui-icon-refresh"></i> 刷新数据
            </button>
            <button class="layui-btn layui-btn-sm layui-btn-normal" id="exportBtn" data-id="{{ current_class.id }}">
                <i class="layui-icon layui-icon-download-circle"></i> 导出报告
            </button>
        </div>
    </div>
    <div class="layui-card-body">
        <div id="reportContainer">
            <div class="report-card">
                <div class="report-header">
                    <div class="report-title" id="reportTitle">课堂报告</div>
                    <div class="report-info" id="reportInfo">报告信息</div>
                </div>
                <div class="report-content">
                    <!-- 统计概览 -->
                    <div class="stats-grid">
                        <div class="stat-item accent-border-left">
                            <div class="stat-number" id="attendanceCount">0</div>
                            <div class="stat-label">签到人数</div>
                        </div>
                        <div class="stat-item accent-border-left">
                            <div class="stat-number" id="totalStudents">0</div>
                            <div class="stat-label">班级总人数</div>
                        </div>
                        <div class="stat-item accent-border-left">
                            <div class="stat-number" id="attendanceRate">0%</div>
                            <div class="stat-label">出勤率</div>
                        </div>
                        <div class="stat-item accent-border-left">
                            <div class="stat-number" id="danmakuCount">0</div>
                            <div class="stat-label">互动次数(弹幕)</div>
                        </div>
                        <div class="stat-item accent-border-left">
                            <div class="stat-number" id="interactionRate">0%</div>
                            <div class="stat-label">参与率</div>
                        </div>
                    </div>

                    <!-- 考勤详情 -->
                    <div class="section-title accent-border-left">考勤详情</div>
                    <div class="attendance-list" id="attendanceDetails">
                        <!-- 动态生成考勤详情 -->
                    </div>

                    <!-- 学生互动情况 -->
                    <div class="section-title accent-border-left">学生互动情况（弹幕）</div>
                    <div id="danmakuDetails">
                        <!-- 动态生成弹幕详情 -->
                    </div>

                    <!-- 作业情况 -->
                    <div class="section-title accent-border-left">作业情况</div>
                    <div id="homeworkDetails">
                        <!-- 动态生成作业详情 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% else %}
<div class="no-class-selected">
    <i class="layui-icon layui-icon-tips"></i>
    <p class="no-class-prompt">请先选择课堂</p>
    <p class="no-class-guidance">
        <a href="/teacher">点击这里</a> 选择要查看报告的课堂
    </p>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
layui.use(['layer', 'element'], function(){
    var layer = layui.layer;
    var element = layui.element;

    var refreshInterval = null;

    // 刷新数据按钮
    $('#refreshBtn').click(function(){
        loadRealtimeData();
    });

    // 导出报告按钮
    $('#exportBtn').click(function(){
        var courseId = $(this).data('id');
        window.open('/teacher/export_report/' + courseId, '_blank');
        layer.msg('报告导出中...');
    });

    // 加载实时数据
    function loadRealtimeData() {
        var courseId = '{{ current_class.id if current_class else "" }}';
        if (!courseId) return;

        $.ajax({
            url: '/teacher/get_realtime_data/' + courseId,
            type: 'GET',
            success: function(res){
                if(res.status === 'success'){
                    displayReport(res.data);
                } else {
                    layer.msg('获取数据失败: ' + res.message);
                }
            },
            error: function(xhr){
                layer.msg('请求失败，请检查网络连接');
            }
        });
    }

    // 显示报告数据
    function displayReport(data) {
        // 更新报告标题和信息
        $('#reportTitle').text(data.course_name + ' - 课堂报告');
        $('#reportInfo').text('更新时间：' + data.report_date + ' | 教室：' + data.classroom_name + ' | 班级：' + data.class_name);

        // 更新统计数据
        $('#attendanceCount').text(data.attendance_count);
        $('#totalStudents').text(data.total_students);
        var attendanceRate = data.total_students > 0 ? ((data.attendance_count / data.total_students) * 100).toFixed(1) : 0;
        $('#attendanceRate').text(attendanceRate + '%');
        $('#danmakuCount').text(data.danmaku_count);
        $('#interactionRate').text(((data.active_students / data.total_students) * 100).toFixed(1) + '%');

        // 更新考勤详情
        var attendanceHtml = '';
        if(data.attendance_details && data.attendance_details.length > 0) {
            data.attendance_details.forEach(function(student) {
                var className = student.status === 'present' ? 'student-present' : 'student-absent';
                var statusText = student.status === 'present' ? '已签到' : '未签到';
                attendanceHtml += '<span class="student-item ' + className + '">' + student.name + ' (' + statusText + ')</span>';
            });
        } else {
            attendanceHtml = '<p class="empty-data-message">暂无考勤数据</p>';
        }
        $('#attendanceDetails').html(attendanceHtml);

        // 更新弹幕详情
        var danmakuHtml = '';
        if(data.danmaku_details && data.danmaku_details.length > 0) {
            data.danmaku_details.forEach(function(student) {
                danmakuHtml += '<div class="homework-item">';
                danmakuHtml += '<div class="homework-title">' + student.student_name + '</div>';
                danmakuHtml += '<div class="homework-stats">发送弹幕数：' + student.danmaku_count + '条</div>';
                danmakuHtml += '</div>';
            });
        } else {
            danmakuHtml = '<p class="empty-data-message">暂无弹幕数据</p>';
        }
        $('#danmakuDetails').html(danmakuHtml);

        // 更新作业详情
        var homeworkHtml = '';
        if(data.homework_details && data.homework_details.length > 0) {
            data.homework_details.forEach(function(homework) {
                homeworkHtml += '<div class="homework-item">';
                homeworkHtml += '<div class="homework-title">' + homework.title + '</div>';
                homeworkHtml += '<div class="homework-stats">';
                homeworkHtml += '题目数量：' + homework.question_count + '题 | ';
                homeworkHtml += '提交人数：' + homework.submitted_count + '/' + homework.total_students + '人';
                if(homework.total_students > 0) {
                    homeworkHtml += ' | 提交率：' + ((homework.submitted_count / homework.total_students) * 100).toFixed(1) + '%';
                }
                homeworkHtml += '</div>';
                homeworkHtml += '</div>';
            });
        } else {
            homeworkHtml = '<p class="empty-data-message">暂无作业数据</p>';
        }
        $('#homeworkDetails').html(homeworkHtml);
    }

    // 启动定时刷新
    function startAutoRefresh() {
        // 每30秒自动刷新一次数据
        refreshInterval = setInterval(function() {
            loadRealtimeData();
        }, 30000);
    }

    // 停止定时刷新
    function stopAutoRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
            refreshInterval = null;
        }
    }

    // 页面加载时初始化
    {% if current_class %}
    // 立即加载数据
    loadRealtimeData();
    // 启动定时刷新
    startAutoRefresh();
    {% endif %}

    // 页面离开时清理定时器
    $(window).on('beforeunload', function() {
        stopAutoRefresh();
    });
});
</script>
{% endblock %}
