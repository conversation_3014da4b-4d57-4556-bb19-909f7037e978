{% extends "teacher/base.html" %}

{% block title %}智慧课堂系统 - 课后作业{% endblock %}

{% block styles %}
<style>
    .current-class-header {
        background: linear-gradient(135deg, #1e9fff 0%, #0084ff 100%);
        color: white;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    .class-info {
        font-size: 16px;
        margin-bottom: 5px;
    }
    .homework-card {
        border: 1px solid #e6e6e6;
        border-radius: 8px;
        margin-bottom: 15px;
        background: #fff;
        overflow: hidden;
        transition: all 0.3s ease;
    }
    .homework-card:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border-color: #1e9fff;
    }
    .homework-header {
        background: #f8f9fa;
        padding: 15px;
        border-bottom: 1px solid #e6e6e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .homework-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
    }
    .homework-meta {
        font-size: 12px;
        color: #666;
    }
    .homework-body {
        padding: 15px;
    }
    .homework-desc {
        color: #666;
        margin-bottom: 10px;
    }
    .homework-stats {
        display: flex;
        gap: 20px;
        margin-bottom: 10px;
    }
    .stat-item {
        font-size: 12px;
        color: #999;
    }
    .stat-number {
        font-weight: bold;
        color: #1e9fff;
    }
    .homework-actions {
        text-align: right;
    }
    .status-badge {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 11px;
        font-weight: bold;
    }
    .status-draft {
        background: #f0f0f0;
        color: #666;
    }
    .status-published {
        background: #e6f7ff;
        color: #1890ff;
    }
    .status-closed {
        background: #fff2e8;
        color: #fa8c16;
    }
    .no-homework {
        text-align: center;
        padding: 40px;
        color: #999;
    }
</style>
{% endblock %}

{% block content %}
{% if current_class %}
<div class="layui-card">
    <div class="layui-card-header">
        <i class="layui-icon layui-icon-edit"></i> 作业
        <div style="float: right;">
            <button class="layui-btn layui-btn-sm layui-btn-normal" id="add-homework-btn">
                <i class="layui-icon layui-icon-add-1"></i> 新建作业
            </button>
            <button class="layui-btn layui-btn-sm layui-btn-primary" id="import-paper-btn">
                <i class="layui-icon layui-icon-download-circle"></i> 导入试卷
            </button>
        </div>
    </div>
    <div class="layui-card-body">
        {% if homework_list %}
        <div id="homework-container">
            {% for homework in homework_list %}
            <div class="homework-card" data-homework-id="{{ homework.id }}">
                <div class="homework-header">
                    <div>
                        <div class="homework-title">
                            {{ homework.title }}
                            <span class="status-badge status-{{ homework.status }}">
                                {% if homework.status == 'draft' %}草稿
                                {% elif homework.status == 'published' %}已发布
                                {% elif homework.status == 'closed' %}已截止
                                {% endif %}
                            </span>
                        </div>
                        <div class="homework-meta">
                            创建时间：{{ homework.created_at[:19] }}
                            {% if homework.deadline %}
                            | 截止时间：{{ homework.deadline[:19] }}
                            {% endif %}
                        </div>
                    </div>
                    <div>
                        <button class="layui-btn layui-btn-xs layui-btn-primary view-homework-btn" data-homework-id="{{ homework.id }}">
                            <i class="layui-icon layui-icon-search"></i> 查看
                        </button>
                        <button class="layui-btn layui-btn-xs layui-btn-normal analysis-btn" data-homework-id="{{ homework.id }}">
                            <i class="layui-icon layui-icon-chart"></i> 分析
                        </button>
                        {% if homework.status == 'draft' %}
                        <button class="layui-btn layui-btn-xs layui-btn-warm publish-btn" data-homework-id="{{ homework.id }}">
                            <i class="layui-icon layui-icon-release"></i> 发布
                        </button>
                        {% endif %}
                        <button class="layui-btn layui-btn-xs layui-btn-danger delete-homework-btn" data-homework-id="{{ homework.id }}">
                            <i class="layui-icon layui-icon-delete"></i> 删除
                        </button>
                    </div>
                </div>
                <div class="homework-body">
                    {% if homework.description %}
                    <div class="homework-desc">{{ homework.description }}</div>
                    {% endif %}
                    <div class="homework-stats">
                        <div class="stat-item">
                            题目数量：<span class="stat-number">{{ homework.question_count or 0 }}</span>
                        </div>
                        <div class="stat-item">
                            提交人数：<span class="stat-number">{{ homework.submitted_count or 0 }}</span>
                        </div>
                        <div class="stat-item">
                            总人数：<span class="stat-number">{{ homework.total_students or 0 }}</span>
                        </div>
                        {% if homework.submitted_count and homework.total_students %}
                        <div class="stat-item">
                            完成率：<span class="stat-number">{{ "%.1f"|format(homework.submitted_count / homework.total_students * 100) }}%</span>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="no-homework">
            <i class="layui-icon layui-icon-edit" style="font-size: 48px; color: #d9d9d9;"></i>
            <p style="font-size: 16px; margin-top: 10px;">暂无课后作业</p>
            <p style="font-size: 14px; color: #ccc;">点击新建作业或导入试卷开始</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 导入试卷弹窗 -->
<div id="import-paper-modal" style="display: none; padding: 20px;">
    <div class="layui-form">
        <div class="layui-form-item">
            <label class="layui-form-label">选择试卷</label>
            <div class="layui-input-block">
                <div id="papers-list" style="max-height: 300px; overflow-y: auto;">
                    <!-- 动态加载试卷列表 -->
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">作业设置</label>
            <div class="layui-input-block">
                <div class="layui-form-item">
                    <label class="layui-form-label">作业标题</label>
                    <div class="layui-input-block">
                        <input type="text" name="homework_title" placeholder="请输入作业标题" class="layui-input" required>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">截止时间</label>
                    <div class="layui-input-block">
                        <input type="datetime-local" name="deadline" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">作业描述</label>
                    <div class="layui-input-block">
                        <textarea name="description" placeholder="可选，描述作业要求" class="layui-textarea"></textarea>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="button" class="layui-btn" id="confirm-import-paper-btn">
                    <i class="layui-icon layui-icon-ok"></i> 导入并创建作业
                </button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </div>
    </div>
</div>

{% else %}
<div style="text-align: center; padding: 60px; color: #999;">
    <i class="layui-icon layui-icon-tips" style="font-size: 48px; color: #d9d9d9;"></i>
    <p style="font-size: 18px; margin-top: 15px;">请先在首页选择要管理的课堂</p>
    <button class="layui-btn layui-btn-normal" onclick="location.href='/teacher'">
        <i class="layui-icon layui-icon-return"></i> 返回首页
    </button>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
layui.use(['layer', 'form'], function(){
    var layer = layui.layer;
    var form = layui.form;

    {% if current_class %}
    // 新建作业
    $('#add-homework-btn').click(function() {
        // 跳转到新建作业页面，并传递当前课程ID
        window.open('/teacher/create_homework?course_id={{ current_class.id }}', '_blank');
    });

    // 导入试卷
    $('#import-paper-btn').click(function() {
        loadPapers();
        layer.open({
            type: 1,
            title: '从资源库导入试卷',
            content: $('#import-paper-modal'),
            area: ['600px', '600px'],
            btn: false,
            shadeClose: true
        });
    });

    // 加载试卷列表
    function loadPapers() {
        $.ajax({
            url: '/dashboard/get_papers',
            type: 'GET',
            success: function(res) {
                if (res.status === 'success') {
                    renderPapers(res.papers);
                } else {
                    $('#papers-list').html('<p style="text-align: center; color: #999;">加载失败</p>');
                }
            },
            error: function() {
                $('#papers-list').html('<p style="text-align: center; color: #999;">网络错误</p>');
            }
        });
    }

    // 渲染试卷列表
    function renderPapers(papers) {
        var html = '';
        if (papers.length === 0) {
            html = '<p style="text-align: center; color: #999;">资源库中暂无试卷</p>';
        } else {
            papers.forEach(function(paper) {
                html += `
                    <div class="layui-form-item">
                        <input type="radio" name="paper_id" value="${paper.id}" title="${paper.title}" lay-skin="primary">
                        <div style="margin-left: 30px; font-size: 12px; color: #999;">
                            题目数：${paper.question_count || 0} | 创建时间：${paper.created_at.substring(0, 19)}
                        </div>
                    </div>
                `;
            });
        }
        $('#papers-list').html(html);
        form.render('radio');
    }

    // 确认导入试卷
    $('#confirm-import-paper-btn').click(function() {
        var selectedPaperId = $('input[name="paper_id"]:checked').val();
        var homeworkTitle = $('input[name="homework_title"]').val();
        
        if (!selectedPaperId) {
            layer.msg('请选择要导入的试卷', {icon: 2});
            return;
        }
        
        if (!homeworkTitle) {
            layer.msg('请输入作业标题', {icon: 2});
            return;
        }

        var formData = {
            paper_id: selectedPaperId,
            title: homeworkTitle,
            deadline: $('input[name="deadline"]').val(),
            description: $('textarea[name="description"]').val(),
            course_id: '{{ current_class.id }}'
        };

        $.ajax({
            url: '/teacher/import_paper_as_homework',
            type: 'POST',
            data: formData,
            success: function(res) {
                if (res.status === 'success') {
                    layer.msg('作业创建成功', {icon: 1});
                    layer.closeAll();
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    layer.msg('创建失败：' + res.message, {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
    });

    // 查看作业
    $(document).on('click', '.view-homework-btn', function() {
        var homeworkId = $(this).data('homework-id');
        window.open('/teacher/view_homework/' + homeworkId, '_blank');
    });

    // 分析作业
    $(document).on('click', '.analysis-btn', function() {
        var homeworkId = $(this).data('homework-id');
        window.open('/teacher/homework_analysis/' + homeworkId, '_blank');
    });

    // 发布作业
    $(document).on('click', '.publish-btn', function() {
        var homeworkId = $(this).data('homework-id');
        
        layer.confirm('确定要发布这个作业吗？发布后学生即可开始作答。', function(index) {
            $.ajax({
                url: '/teacher/publish_homework/' + homeworkId,
                type: 'POST',
                success: function(res) {
                    if (res.status === 'success') {
                        layer.msg('作业发布成功', {icon: 1});
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        layer.msg('发布失败：' + res.message, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('网络错误，请重试', {icon: 2});
                }
            });
            layer.close(index);
        });
    });

    // 删除作业
    $(document).on('click', '.delete-homework-btn', function() {
        var homeworkId = $(this).data('homework-id');
        
        layer.confirm('确定要删除这个作业吗？此操作不可恢复。', function(index) {
            $.ajax({
                url: '/teacher/delete_homework/' + homeworkId,
                type: 'DELETE',
                success: function(res) {
                    if (res.status === 'success') {
                        layer.msg('作业删除成功', {icon: 1});
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        layer.msg('删除失败：' + res.message, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('网络错误，请重试', {icon: 2});
                }
            });
            layer.close(index);
        });
    });
    {% endif %}
});
</script>
{% endblock %}
