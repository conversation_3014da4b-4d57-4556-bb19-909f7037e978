"""
文件管理服务类
实现基于数据库驱动 + 本地散列化存储的文件管理系统
"""
import os
import uuid
import mimetypes
from datetime import datetime
from flask import current_app
from werkzeug.utils import secure_filename
from app.models.database import get_db


class FileService:
    """文件管理服务类"""
    
    def __init__(self):
        self.upload_base_path = current_app.config.get('UPLOAD_FOLDER', 'uploads')
    
    def _get_teacher_upload_path(self, teacher_id):
        """获取教师的上传目录路径"""
        teacher_path = os.path.join(self.upload_base_path, str(teacher_id))
        if not os.path.exists(teacher_path):
            os.makedirs(teacher_path, exist_ok=True)
        return teacher_path
    
    def _generate_stored_filename(self, original_filename):
        """生成存储用的唯一文件名"""
        file_ext = os.path.splitext(original_filename)[1]
        return f"{uuid.uuid4()}{file_ext}"
    
    def _get_mime_type(self, filename):
        """获取文件的MIME类型"""
        mime_type, _ = mimetypes.guess_type(filename)
        return mime_type or 'application/octet-stream'
    
    def create_folder(self, teacher_id, folder_name, parent_id=None):
        """创建文件夹"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            # 检查同级目录下是否已存在同名文件夹
            if parent_id:
                cursor.execute("""
                    SELECT COUNT(*) FROM t_folders 
                    WHERE teacher_id = ? AND parent_id = ? AND folder_name = ?
                """, (teacher_id, parent_id, folder_name))
            else:
                cursor.execute("""
                    SELECT COUNT(*) FROM t_folders 
                    WHERE teacher_id = ? AND parent_id IS NULL AND folder_name = ?
                """, (teacher_id, folder_name))
            
            if cursor.fetchone()[0] > 0:
                conn.close()
                return False, "文件夹已存在"
            
            # 创建文件夹记录
            cursor.execute("""
                INSERT INTO t_folders (folder_name, parent_id, teacher_id)
                VALUES (?, ?, ?)
            """, (folder_name, parent_id, teacher_id))
            
            folder_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return True, "文件夹创建成功", folder_id
            
        except Exception as e:
            return False, f"创建文件夹失败: {str(e)}", None
    
    def delete_folder(self, teacher_id, folder_id):
        """删除文件夹（级联删除子文件夹，文件移到根目录）"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            # 验证文件夹所有权
            cursor.execute("""
                SELECT id FROM t_folders WHERE id = ? AND teacher_id = ?
            """, (folder_id, teacher_id))
            
            if not cursor.fetchone():
                conn.close()
                return False, "文件夹不存在或无权限"
            
            # 将该文件夹下的文件移动到根目录（folder_id设为NULL）
            cursor.execute("""
                UPDATE t_files SET folder_id = NULL WHERE folder_id = ? AND teacher_id = ?
            """, (folder_id, teacher_id))
            
            # 删除文件夹（级联删除子文件夹）
            cursor.execute("DELETE FROM t_folders WHERE id = ? AND teacher_id = ?", (folder_id, teacher_id))
            
            conn.commit()
            conn.close()
            
            return True, "文件夹删除成功"
            
        except Exception as e:
            return False, f"删除文件夹失败: {str(e)}"
    
    def upload_file(self, teacher_id, file, folder_id=None):
        """上传文件"""
        try:
            if not file or file.filename == '':
                return False, "没有选择文件", None
            
            # 获取原始文件名并安全化
            original_filename = secure_filename(file.filename)
            if not original_filename:
                return False, "无效的文件名", None
            
            # 生成存储文件名
            stored_filename = self._generate_stored_filename(original_filename)
            
            # 获取教师上传目录
            teacher_path = self._get_teacher_upload_path(teacher_id)
            
            # 构建文件存储路径
            file_path = os.path.join(teacher_path, stored_filename)
            
            # 保存文件
            file.save(file_path)
            
            # 获取文件信息
            file_size = os.path.getsize(file_path)
            mime_type = self._get_mime_type(original_filename)
            
            # 计算相对路径（相对于uploads目录）
            relative_path = os.path.relpath(file_path, self.upload_base_path)
            relative_path = relative_path.replace(os.sep, '/')  # 统一使用正斜杠
            
            # 保存到数据库
            conn = get_db()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO t_files (original_filename, stored_filename, file_path, 
                                   file_size, mime_type, folder_id, teacher_id)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (original_filename, stored_filename, relative_path, 
                  file_size, mime_type, folder_id, teacher_id))
            
            file_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return True, "文件上传成功", file_id
            
        except Exception as e:
            # 如果数据库操作失败，删除已保存的文件
            if 'file_path' in locals() and os.path.exists(file_path):
                os.remove(file_path)
            return False, f"上传文件失败: {str(e)}", None
    
    def delete_file(self, teacher_id, file_id):
        """删除文件"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            # 获取文件信息
            cursor.execute("""
                SELECT file_path FROM t_files WHERE id = ? AND teacher_id = ?
            """, (file_id, teacher_id))
            
            file_record = cursor.fetchone()
            if not file_record:
                conn.close()
                return False, "文件不存在或无权限"
            
            # 删除数据库记录
            cursor.execute("DELETE FROM t_files WHERE id = ? AND teacher_id = ?", (file_id, teacher_id))
            
            # 删除物理文件
            file_path = os.path.join(self.upload_base_path, file_record['file_path'])
            if os.path.exists(file_path):
                os.remove(file_path)
            
            conn.commit()
            conn.close()
            
            return True, "文件删除成功"
            
        except Exception as e:
            return False, f"删除文件失败: {str(e)}"
    
    def move_file(self, teacher_id, file_id, target_folder_id):
        """移动文件到指定文件夹"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            # 验证文件所有权
            cursor.execute("""
                SELECT id FROM t_files WHERE id = ? AND teacher_id = ?
            """, (file_id, teacher_id))
            
            if not cursor.fetchone():
                conn.close()
                return False, "文件不存在或无权限"
            
            # 如果目标文件夹不为空，验证文件夹所有权
            if target_folder_id:
                cursor.execute("""
                    SELECT id FROM t_folders WHERE id = ? AND teacher_id = ?
                """, (target_folder_id, teacher_id))
                
                if not cursor.fetchone():
                    conn.close()
                    return False, "目标文件夹不存在或无权限"
            
            # 更新文件的文件夹ID
            cursor.execute("""
                UPDATE t_files SET folder_id = ? WHERE id = ? AND teacher_id = ?
            """, (target_folder_id, file_id, teacher_id))
            
            conn.commit()
            conn.close()
            
            return True, "文件移动成功"
            
        except Exception as e:
            return False, f"移动文件失败: {str(e)}"
    
    def rename_file(self, teacher_id, file_id, new_name):
        """重命名文件"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            # 验证文件所有权
            cursor.execute("""
                SELECT original_filename FROM t_files WHERE id = ? AND teacher_id = ?
            """, (file_id, teacher_id))
            
            file_record = cursor.fetchone()
            if not file_record:
                conn.close()
                return False, "文件不存在或无权限"
            
            # 安全化新文件名
            new_name = secure_filename(new_name)
            if not new_name:
                conn.close()
                return False, "无效的文件名"
            
            # 更新文件名
            cursor.execute("""
                UPDATE t_files SET original_filename = ? WHERE id = ? AND teacher_id = ?
            """, (new_name, file_id, teacher_id))
            
            conn.commit()
            conn.close()
            
            return True, "文件重命名成功"
            
        except Exception as e:
            return False, f"重命名文件失败: {str(e)}"
    
    def get_file_info(self, teacher_id, file_id):
        """获取文件信息"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT f.*, fo.folder_name 
                FROM t_files f
                LEFT JOIN t_folders fo ON f.folder_id = fo.id
                WHERE f.id = ? AND f.teacher_id = ?
            """, (file_id, teacher_id))
            
            file_info = cursor.fetchone()
            conn.close()
            
            if file_info:
                return True, "获取成功", dict(file_info)
            else:
                return False, "文件不存在或无权限", None
                
        except Exception as e:
            return False, f"获取文件信息失败: {str(e)}", None
    
    def get_folder_tree(self, teacher_id):
        """获取文件夹树结构"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, folder_name, parent_id, created_at
                FROM t_folders 
                WHERE teacher_id = ?
                ORDER BY parent_id, folder_name
            """, (teacher_id,))
            
            folders = cursor.fetchall()
            conn.close()
            
            return True, "获取成功", [dict(folder) for folder in folders]
            
        except Exception as e:
            return False, f"获取文件夹树失败: {str(e)}", []
    
    def get_files_in_folder(self, teacher_id, folder_id=None, search_term=None):
        """获取指定文件夹中的文件列表"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = ["teacher_id = ?"]
            params = [teacher_id]
            
            if folder_id is None:
                where_conditions.append("folder_id IS NULL")
            else:
                where_conditions.append("folder_id = ?")
                params.append(folder_id)
            
            if search_term:
                where_conditions.append("original_filename LIKE ?")
                params.append(f"%{search_term}%")
            
            query = f"""
                SELECT f.*, fo.folder_name 
                FROM t_files f
                LEFT JOIN t_folders fo ON f.folder_id = fo.id
                WHERE {' AND '.join(where_conditions)}
                ORDER BY f.upload_at DESC
            """
            
            cursor.execute(query, params)
            files = cursor.fetchall()
            conn.close()
            
            return True, "获取成功", [dict(file) for file in files]
            
        except Exception as e:
            return False, f"获取文件列表失败: {str(e)}", []
